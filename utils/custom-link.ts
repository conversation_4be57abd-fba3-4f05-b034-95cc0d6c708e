import { doMagic, undoMagic } from './abracadabra';

export function generateSearch(topicName: string, words: string[]) {
  const params = new URLSearchParams({
    c: '1',
    topicName,
    words: words.map(w => doMagic(w.toLowerCase())).join(','),
  });
  return params.toString();
}

export function parseSearch() {
  const url = useRequestURL();
  const params = url.searchParams;
  const sharedTopicName = params.get('topicName') || '';
  const sharedWords = params.get('words');
  const words = (sharedWords || '').split(',').map(w => undoMagic(w));
  const isShared = !!params.get('c') && !!sharedTopicName && !!sharedWords;
  return {
    isShared,
    topicName: sharedTopicName,
    words,
  };
}
