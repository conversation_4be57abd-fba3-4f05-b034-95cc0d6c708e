import capitalize from 'lodash-es/capitalize';

export function sleep(duration = 1000):Promise<void> {
  return new Promise((resolve) => {
    setTimeout(resolve, duration);
  });
}

export function name2slug(langName: string) {
  return langName.toLowerCase()
    .replace('(', '-')
    .replace(')', '');
}

export function slug2name(langSlug: string) {
  const [language, speech] = langSlug.split('-');
  langSlug = capitalize(language);
  if (speech) {
    langSlug += `(${speech.toUpperCase()})`;
  }
  return langSlug;
}

function fallbackCopyTextToClipboard(text:string):void {
  const textArea = document.createElement('textarea');
  textArea.value = text;

  // Avoid scrolling to bottom
  textArea.style.top = '0';
  textArea.style.left = '0';
  textArea.style.position = 'fixed';

  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  document.execCommand('copy');
  document.body.removeChild(textArea);
}
export async function copyTextToClipboard(text:string) {
  if (!navigator.clipboard) {
    fallbackCopyTextToClipboard(text);
    return;
  }
  await navigator.clipboard.writeText(text);
}
