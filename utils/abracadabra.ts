const SALT = '19840325761';

export function doMagic(word:string):string {
  word = word.toLowerCase();
  word = word.split('').map((letter:string, index:number) => {
    let code = letter.charCodeAt(0);
    // move
    code += parseInt(SALT.charAt(index));
    return String.fromCharCode(code);
  }).join('');
  return encodeURIComponent(word);
}

export function undoMagic(word:string):string {
  word = decodeURIComponent(word);
  return word.split('').map((letter:string, index:number) => {
    let code = letter.charCodeAt(0);
    code -= parseInt(SALT.charAt(index));
    return String.fromCharCode(code);
  }).join('');
}
