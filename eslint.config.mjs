// @ts-check
import withNuxt from './.nuxt/eslint.config.mjs'

export default withNuxt(
  // Your custom configs here
  {
    ignores: [
      'dist',
      'node_modules',
      'test',
      'tmp',
      'cypress.config.ts',
      'cypress',
      '.nuxt',
      '.output',
    ],
    rules: {
      'quotes': [ 'error', 'single' ],
      'object-curly-spacing': [ 'error', 'always' ],
      'computed-property-spacing': [ 'error', 'always' ],
    },
  },
)
