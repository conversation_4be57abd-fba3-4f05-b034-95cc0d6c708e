export type LanguageItem = {
  short: string;
  flag: string;
}

export type Heading = {
  h1: string;
  title: string;
}

export type EsModule<T> = {
  default: T
}

export type ShareTextsItem = {
  newGame:string;
  playing:string;
  success:string;
  fail:string;
}

export type ShareTexts = {
  custom:ShareTextsItem;
  unlimited:ShareTextsItem;
}

export interface ShareQuery extends URLSearchParams{
  w?:string;
  tid?:string;
}

export type CellItem = {
  letter: string;
  score: number;
}

export type RowItem = CellItem[];

export type History = {
  played:number;
  win:number;
  streak:number;
  maxStreak:number;
  stats:number[];
  lastWin:boolean;
}

export type HistoryPayload = {
  win:boolean;
  level:number;
}

export type LetterNumbers = 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 100;

export type LanguagePayload = {
  langName?:string;
  langSlug?:string;
  letter?:number;
  reset?:boolean;
}

export interface IDictionary<TValue> {
  [key:string]:TValue;
}

export type GameModePayload = {
  letterNumber: LetterNumbers;
}

export type CustomProgressPayload = {
  word: string;
  topicId: string | number
}

export type CustomGamePayload = {
  word: string;
  masked: string;
  topicId: string;
}

export type GamesRecord = Partial<Record<LetterNumbers, GameState>>;

export type Topic = {
  title?: string;
  topicId: number;
  topicItems: TopicItem[];
}

export type TopicItem = {
  medal: string;
  placeholder: string;
  url: string;
  word: string;
  hashed: string;
  id: number | string;
}

export type TopicProgress = string[];

export interface TopicCate {
  category: string,
  topicId: number;
  language?: string,
  topics: Record<string, {
    id: number,
    words: string[]
  }>
}

export interface CustomGame {
  title: string,
  words: string[]
}
export interface Config {
  darkMode?:boolean;
  autoDarkMode?:boolean;
  highContrast:boolean;
  letterNumber:LetterNumbers;
  topics?:Nullable<Record<string, string[]>>;
  fixedTopic?: number;
  currentCateName: string,
  sharedTopicCate?: TopicCate,
  customInfo?: CustomGame,
}

export interface GameState {
  rows:RowItem[];
  startTime:number;
  lastUpdatedTime:number;
  topicId?: string | number,
  word:string;
  masked?:string;
  win:boolean;
  done:boolean;
}

export type ApiResponse<T> = {
  code: number;
  data: T;
  meta?: {
    [key: string]: unknown;
  };
}
