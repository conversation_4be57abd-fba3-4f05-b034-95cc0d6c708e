import type { H3Event } from 'h3';
import English from '~/data/en-top10.json';
import French from '~/data/french-top10.json';
import Portuguese from '~/data/portuguese-top10.json';
import Spanish from '~/data/spanish-top10.json';
import type { TopicCate } from '~/types';

export default defineEventHandler(async function (event: H3Event){
  const lang = (getQuery(event) as { lang: string}).lang || 'english';
  const jsonMap: Record<string, TopicCate[]> = {
    english: English,
    french: French,
    portuguese: Portuguese,
    spanish: Spanish,
  }
  if (!(lang in jsonMap)) {
    return {
      code: 0,
      data: [],
    };
  }

  const data = jsonMap[ lang ] as TopicCate[];
  return {
    code: 0,
    data,
  };
});
