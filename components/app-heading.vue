<script lang="ts" setup>
import useStore from '~/store';
import enContent from '~/i18n/content.json';
import otherContent from '~/i18n/content-i18n.json';
import Languages from '~/data/languages';

const store = useStore();
const route = useRoute();
const title = computed<string>(() => {
  const isEnglish = !route.params.slug;
  const h1 = isEnglish ? enContent.h1 : otherContent.h1;
  return h1.replace('${flag} ', () => Languages[ store.langName ].flag)
    .replace(/\s+unlimited$/i, '');
});
const titleMode = computed<string>(() => {
  if (store.isCustom) return 'custom';
  return 'Unlimited';
});
</script>

<template>
  <h1 class="flex-1 text-2xl leading-12 flex justify-center items-center whitespace-nowrap">
    {{title}}
    <span
      v-if="titleMode"
      class="text-sm leading-6 capitalize ml-1 rounded px-1 text-white"
      :class="titleMode"
    >{{titleMode}}</span>
  </h1>
</template>
