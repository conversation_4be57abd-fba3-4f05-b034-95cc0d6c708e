<script lang="ts" setup>
import { CheckIcon } from 'lucide-vue-next';
import { LETTERS } from '@/data';
import useStore from '@/store';
import { sleep } from '@/utils';
import type { LetterNumbers } from '~/types';

const version = __VERSION__;
const store = useStore();
const route = useRoute();
const router = useRouter();

const emit = defineEmits<{
  (e:'close', letter?:number):void;
}>();

const letterNumber = computed(() => store.letterNumber);
const highContrast = computed({
  get() {
    return store.config.highContrast;
  },
  set(value) {
    store.setConfig({ highContrast: value });
  },
});
const isDarkModeAuto = computed(() => !('darkMode' in store.config));
const darkMode = computed({
  get() {
    return 'darkMode' in store.config ? store.config.darkMode : store.config.autoDarkMode;
  },
  set(value) {
    store.setConfig({ darkMode: value });
  },
});
const isCleared = ref<boolean>(false);
const loadingDict = ref<number>(0);
const importStatus = ref<boolean | null>();
const importMessage = ref<string>();

async function onSelectLetter(letter: LetterNumbers) {
  loadingDict.value = letter;
  if (route.query.w) {
    await router.replace({ name: route.name as string });
  }
  await store.setLetterNumber(letter);
  loadingDict.value = 0;
  emit('close');
}

let timeout:Timeout;
function doClearLocalData() {
  if (!confirm('All your records will be deleted. Are you sure?')) {
    return;
  }
  store.clearLocal();
  isCleared.value = true;
  clearTimeout(timeout);
  timeout = setTimeout(() => {
    isCleared.value = false;
  }, 1250);
}
function doImportData(event:Event) {
  importStatus.value = null;
  const target = event.target as HTMLInputElement;
  const [file] = target.files || [];
  if (!file) {
    return;
  }
  const reader = new FileReader();
  reader.onload = async function () {
    const { result } = reader;
    let json;
    try {
      json = JSON.parse(result as string);
    } catch (e) {
      console.error(e);
      importStatus.value = false;
      importMessage.value = 'Not a valid MyWordGame record file';
      return;
    }
    if (!json.games || !json.version || !json.history) {
      importStatus.value = false;
      importMessage.value = 'Not a valid MyWordGame record file';
      return;
    }
    store.setConfig(json);
    importStatus.value = true;
    importMessage.value = 'Imported successfully';
    await sleep(1250);
    location.reload();
  }
  reader.readAsText(file);
}

onBeforeUnmount(() => {
  clearTimeout(timeout);
});
</script>

<template>
<div>
  <h2 class="text-xl leading-none font-semibold border-b pb-4 uppercase">Config</h2>
  <div class="grid grid-cols-4 gap-2 py-3 border-b">
    <button
      v-for="item in LETTERS"
      :key="item"
      class="letter-item text-center rounded leading-loose flex flex-col justify-center items-center py-3"
      :class="{active: item === letterNumber}"
      :disabled="loadingDict > 0"
      type="button"
      @click="onSelectLetter(item)"
    >
    <span
      v-if="loadingDict === item"
      class="loading loading-spinner size-4"
    />
      <span class="font-bold leading-none">{{item}}</span>
      <span class="text-xs font-semibold">letters</span>
    </button>
  </div>
  <div class="py-2 border-b flex justify-between text-sm dark:border-gray-600">
    <span class="font-semibold">
      Dark mode
      <span v-if="isDarkModeAuto" class="font-normal ml-1">(auto)</span>
    </span>
    <input-switch
      v-model="darkMode"
      :class="isDarkModeAuto ? 'opacity-50' : ''"
    />
  </div>
  <div class="py-2 border-b flex justify-between text-sm dark:border-gray-600">
    <span class="font-semibold">High Contrast Mode</span>
    <input-switch v-model="highContrast" />
  </div>
  <div class="py-2 border-b flex justify-between text-sm dark:border-gray-600">
    <span class="font-semibold">Version</span>
    <span>{{version}}</span>
  </div>
  <div class="border-b flex flex-wrap items-center text-sm leading-9 dark:border-gray-600">
    <span class="font-semibold mr-auto">Reset Local Data</span>
    <div v-if="isCleared" class="text-green-500">
      <check-icon class="size-4 mr-2"/>
      Reset Successfully
    </div>
    <button
      v-else
      class="underline-button uppercase"
      type="button"
      @click="doClearLocalData"
    >Reset</button>
    <label class="underline-button uppercase mx-2 cursor-pointer">
      Import
      <input type="file" hidden="hidden" accept="application/json" @change="doImportData">
    </label>
    <export-button class="underline-button uppercase" />
  </div>
  <div class="text-sm text-center leading-9 pt-2 pb-1 dark:border-gray-600">
    <nuxt-link
      class="underline-button block mx-auto w-min uppercase"
      target="_blank"
      href="https://forms.gle/MAUCCdCBzSzFWpp88"
    >Contact Us</nuxt-link>
  </div>
  <aw-notification :status="importStatus" :message="importMessage" />
</div>
</template>
