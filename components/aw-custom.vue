<script lang="ts" setup>
import { CheckIcon, PlusIcon } from 'lucide-vue-next';
import { generateSearch } from '@/utils/custom-link';
import { copyTextToClipboard } from '@/utils';
import useStore from '~/store';

let timeout: Timeout;
const store = useStore();
const topicName = ref<string>(store.customInfo.title || '');
const words = ref<string[]>(store.customInfo.words || []);
const isAddButtonVisible = computed(() => words.value.length < 10);
const message = ref('');
const isCopied = ref(false);
const title = ref<HTMLInputElement>();
const inputs = ref<HTMLInputElement[]>();

const shareUrl = computed<string>(() => {
  if (!topicName.value || words.value.filter(Boolean).length === 0) {
    return '';
  }
  return generateLink(topicName.value, words.value.filter(Boolean));
});

async function doCopyLink() {
  clearTimeout(timeout);
  isCopied.value = true;
  await copyTextToClipboard(shareUrl.value);
  timeout = setTimeout(() => {
    isCopied.value = false;
  }, 1250);
}
function doSave() {
  message.value = ''
  if (!topicName.value) {
    message.value = 'Topic name is required!';
    return;
  }
  const validWords = words.value.filter(Boolean);
  if (validWords.length <= 0) {
    message.value = 'A topic must contain at least one word!';
    return;
  }

  store.setCustomGame(topicName.value, words.value);
  doCopyLink();
}

async function addWord() {
  words.value.push('');
  await nextTick();
  if (Array.isArray(inputs.value)) {
    inputs.value[ inputs.value.length - 1 ].focus();
  }
}
function generateLink(topicName: string, words: string[]) {
  const url:URL = new URL(location.href);
  url.search = generateSearch(topicName, words);
  return url.toString();
}

watch(() => store.customInfo, (newValue) => {
  if (newValue) {
    topicName.value = newValue.title || '';
    words.value = newValue.words || [];
  }
}, { immediate: true });
</script>

<template>
<form
  class="aw-custom theme-item mb-4 border rounded-md mx-auto w-11/12 md:w-1/3"
  @submit.prevent="doSave"
>
  <header class="border-b flex justify-between p-2">
    <input
      ref="title"
      v-model.trim="topicName"
      class="font-bold w-full text-center leading-8 focus:outline-blue-500 placeholder:underline placeholder:decoration-dashed placeholder:underline-offset-2"
      required
      placeholder="Enter Topic Name"
      @focus="store.setKeyboardDisabled(true)"
      @blur="store.setKeyboardDisabled(false)"
    >
  </header>
  <div class="font-mono p-2 grid grid-cols-2 gap-2 xs:grid-cols-2">
    <input
      v-for="(item, index) in words"
      ref="inputs"
      :key="index"
      v-model.trim="words[index]"
      class="px-2 py-1 uppercase whitespace-nowrap border-b border-gray-400 border-solid focus:outline-none"
      :maxlength="11"
      @focus="store.setKeyboardDisabled(true)"
      @blur="store.setKeyboardDisabled(false)"
    >
    <button
      v-if="isAddButtonVisible"
      class="rounded px-2 py-1 uppercase whitespace-nowrap border border-blue-500 text-blue-500 hover:bg-blue-100 inline-flex gap-1 items-center"
      type="button"
      @click="addWord"
    >
      <plus-icon class="size-4" />
      Add Word
    </button>
  </div>
  <div class="p-2 pt-0">
    <button
      class="w-full flex items-center justify-center px-2 py-1 leading-loose mx-auto text-white rounded"
      :class="isCopied ? 'bg-green-500' : 'bg-green-600 dark:bg-green-700 hover:bg-green-500'"
      :disabled="!topicName || words.filter(Boolean).length === 0"
    >
      <check-icon v-if="isCopied" class="size-4 mr-1" />
      {{ isCopied ? 'Copied' : 'Save' }}
    </button>
    <div
      v-if="message"
      class="text-red-500 mt-2 dark:bg-red-700 dark:bg-transparent"
    >{{message}}</div>
  </div>
  <share-buttons
    v-if="shareUrl"
    button-class="p-2"
    :share-url="shareUrl"
    url-class="px-2"
  />
</form>
</template>


<style>
.dark .aw-custom input {
  background-color: var(--color-bg);
}
</style>
