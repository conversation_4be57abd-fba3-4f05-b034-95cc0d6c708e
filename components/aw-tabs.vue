<template>
<div class="aw-tab container mx-auto flex items-center overflow-x-auto">
  <template
    v-for="(item, index) in list"
    :key="index"
  >
    <input
      :id="`tab${index}`"
      v-model="localValue"
      hidden="hidden"
      name="tabs"
      type="radio"
      :value="item"
    >
    <label
      class="shrink-0 rounded-t px-4 py-2 cursor-pointer text-key-color"
      :for="`tab${index}`"
    >{{ item }}</label>
  </template>
</div>
</template>

<script lang="ts" setup>
defineProps<{
  list: string[],
}>();
const localValue = defineModel<string>('modelValue');
</script>

<style>
input:checked + label {
  background-color: var(--color-bg);
  color: white
}
</style>
