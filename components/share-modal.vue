<script lang="ts" setup>
import { DEFAULT_LANGUAGE, MAX_LETTER_NUMBER, MIN_LETTER_NUMBER } from '@/data';
import { CheckIcon } from 'lucide-vue-next';
import type { CellItem, RowItem, ShareTextsItem } from '~/types';
import { doMagic } from '@/utils/abracadabra';
import { copyTextToClipboard } from '@/utils';
import Languages from '@/data/languages';
import useGameStore from '~/store/game';
import allShareTexts from '~/data/share';
import useStore from '~/store';
import noop from 'lodash-es/noop';

const emit = defineEmits<{
  (e: 'message', message:string): void;
  (e: 'close'):void;
}>();
type Props = {
  isWelcome?: boolean;
};
const props = withDefaults(defineProps<Props>(), {
  isWelcome: false,
});
const route = useRoute();
const router = useRouter();
const store = useStore();
const gameStore = useGameStore();
let timeout: Timeout;

const isUpperCopied = ref<boolean>(false);
const isCurrentCopied = ref<boolean>(false);
const isGiveUp = ref<boolean>(false);
const customWord = ref<string>('');

const level = computed<number>(() => gameStore.level);
const resultGrid = computed<string>(() => {
  const { rows = [], done } = gameStore.gameState || {};
  if (!done) return '';
  return rows.map((row: RowItem) => {
    return row.map((cell: CellItem) => {
      switch (cell.score) {
        case 1: return store.config.highContrast ? '🟦' : '🟨';
        case 10: return store.config.highContrast ? '🟧' : '🟩';
      }
      return store.config.highContrast ? '⬛' : '⬜';
    }).join('');
  }).join('\n');
});
const shareUrl = computed<string>(() => {
  if (customWord.value.length < MIN_LETTER_NUMBER || customWord.value.length > MAX_LETTER_NUMBER) {
    return '';
  }
  return getShareUrl(customWord.value);
});
const shareTexts = computed<ShareTextsItem>(() => {
  if (store.isCustom) {
    return allShareTexts.custom;
  }
  return allShareTexts.unlimited;
});
const shareText = computed<string>(() => {
  if (isSuccessful.value) {
    return shareTexts.value.success;
  }
  if (isFailed.value) {
    return shareTexts.value.fail;
  }
  if (isNewGame.value) {
    return shareTexts.value.newGame;
  }
  return shareTexts.value.playing;
});
const isFailed = computed<boolean>(() =>
  !!(gameStore.gameState?.done && !gameStore.gameState.win));
const isSuccessful = computed<boolean>(() =>
  !!(gameStore.gameState?.done && gameStore.gameState.win));
const isNewGame = computed<boolean>(() => gameStore.level === 0);
const nextRoundLabel = computed<string>(() => {
  if (store.fixedTopic && route.query.w) {
    return `Next "${store.fixedTopic.title}"`;
  }
  return 'Next round';
});

function getShareUrl(word:string):string {
  const url:URL = new URL(location.href);
  const search = new URLSearchParams({
    w: doMagic(word),
  });
  url.pathname = (store.langSlug ? 'wordle-' + store.langSlug : '') + '/';
  url.search = search.toString();
  return url.toString();
}
async function doCopy() {
  const text = shareText.value.replace(/\${(\w+)}/g, (matched:string, key:string):string => {
    switch (key) {
      case 'index':
        return '';

      case 'letter':
        return store.letterNumber.toString();

      case 'level':
        return level.value.toString();

      case 'max_step':
        return store.maxStep.toString();

      case 'grid':
        return '\n' + resultGrid.value + '\n';

      case 'mask':
        return gameStore.gameState ? String(gameStore.gameState.masked) : '';

      case 'lang_name':
        return store.langName === DEFAULT_LANGUAGE ? '' : store.langName;

      case 'lang_slug':
        return store.langSlug;

      case 'flag':
        return store.langName === DEFAULT_LANGUAGE ? '' : Languages[ store.langName ].flag;
    }
    return matched;
  });
  try {
    await copyTextToClipboard(text);
    emit('message', 'Result copied to clipboard');
    isUpperCopied.value = true;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      isUpperCopied.value = false;
    }, 1250);
  } catch (e) {
    console.error(e);
    emit('message', 'Failed to copy result');
  }
  navigator?.share({
    title: document.title,
    text,
    url: location.href,
  }).catch(noop);
}
async function doCopyCurrent() {
  if (!gameStore.gameState) return;

  clearTimeout(timeout);
  isCurrentCopied.value = true;
  await copyTextToClipboard(getShareUrl(gameStore.gameState.word));
  timeout = setTimeout(() => {
    isCurrentCopied.value = false;
  }, 1250);
}
function doRetry() {
  gameStore.retry();
  emit('close');
}
function doGiveUp() {
  isGiveUp.value = true;
}
function doNextRound() {
  if (store.fixedTopic && route.query.w) {
    const next = store.fixedTopic.topicItems.find(item => !store.topicProgress.includes(`${store.config.fixedTopic}-${item.word}`));
    router.push({
      name: route.name as string,
      query: {
        w: next?.hashed,
        tid: store.config.fixedTopic,
      },
    });
  } else {
    gameStore.restart();
  }
  emit('close');
}
</script>

<template>
<h2 class="text-center text-xl font-semibold uppercase mb-3">
  <template v-if="props.isWelcome">Custom Wordle</template>
  <template v-else>{{isSuccessful ? '🥳 ' : ''}}{{isFailed ? '😭 ' : ''}}Wordle {{level}}/{{store.maxStep}}</template>
</h2>
<template v-if="!props.isWelcome">
  <button
    class="share-button pressable-button rounded-xl text-white text-lg capitalize mx-auto mb-2 flex items-center justify-center font-semibold w-10/12"
    type="button"
    @click="doCopy"
  >
    <template v-if="isUpperCopied">
      <check-icon class="mr-2 size-4" />
      Copied
    </template>
    <template v-else>Copy {{isSuccessful || isFailed ? 'result' : ''}} to clipboard</template>
  </button>
  <button
    class="share-button pressable-button rounded-xl text-white text-lg capitalize mx-auto mb-2 flex items-center justify-center font-semibold w-10/12"
    type="button"
    @click="doCopyCurrent"
  >
    <template v-if="isCurrentCopied">
      <check-icon class="mr-2 size-4" />
      Copied
    </template>
    <template v-else>Copy link to this word</template>
  </button>
  <template v-if="isSuccessful || isFailed">
    <button
      class="pressable-button rounded-xl bg-sky-600 text-white capitalize text-xl mx-auto mb-2 flex items-center justify-center w-10/12 dark:bg-sky-700"
      type="button"
      @click="doNextRound"
    >{{ nextRoundLabel }}</button>
  </template>
  <template v-if="isFailed">
    <template v-if="!isGiveUp">
      <button
        class="pressable-button rounded-xl bg-cyan-500 text-white text-center capitalize text-xl mx-auto mb-2 block w-10/12 dark:bg-cyan-600"
        type="button"
        @click="doRetry"
      >Retry</button>
      <button
        class="pressable-button rounded-xl bg-pink-600 text-white text-center capitalize text-xl mx-auto mb-2 block w-10/12 dark:bg-pink-700"
        type="button"
        @click="doGiveUp"
      >Click to see the answer</button>
    </template>
    <div
      v-if="isGiveUp"
      class="text-center mb-4 py-2"
    >
      The word is:
      <span class="uppercase font-bold text-red-500 ml-2">{{ gameStore.gameState?.word || '' }}</span>
    </div>
  </template>
</template>
<hr class="my-3 dark:border-gray-600">
<h2 class="text-center text-xl font-semibold capitalize mb-1">Make your own Wordle:</h2>
  <p class="text-center text-xs">You can type a word with {{MIN_LETTER_NUMBER}}~{{MAX_LETTER_NUMBER}} letters.</p>
  <form
    id="form"
    class="my-2 mx-auto w-10/12"
  >
    <input
      ref="input"
      v-model="customWord"
      class="share-input h-10 rounded px-3 font-mono text-center block w-full"
      placeholder="Start typing..."
      autofocus
      required
    >
  </form>
  <share-buttons
    :share-url="shareUrl"
    url-class="text-center mb-2"
  />
</template>
