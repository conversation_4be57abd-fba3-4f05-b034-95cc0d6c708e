<script setup lang="ts">
import useStore from '~/store';
import type { App<PERSON>ead<PERSON>, GameGrid } from '#components';
import useGameStore from '~/store/game';
import type { CellItem, RowItem } from '~/types';
import { initialLoading } from '~/services';

const store = useStore();
const gameStore = useGameStore();

const message = ref<string>('Loading');
const hasConfetti = ref<boolean>(false);
const header = ref<InstanceType<typeof AppHeader>>();
const grid = ref<InstanceType<typeof GameGrid>>();

function onKeypress(key: string) {
  console.log('onKeypress', key);
  // fix #224: if modal is open, not input any key
  if (header.value?.showModal) {
    return;
  }
  console.log('not showModal');
  if (gameStore.gameState?.done && header.value) {
    (header.value as InstanceType<typeof AppHeader>).doOpenModal();
    return;
  }
  console.log('grid handle');
  grid.value?.pressKey(key);
}

function onEnter() {
  // restart a game
  if (gameStore.gameState?.done) {
    gameStore.restart();
    return;
  }

  const word = grid.value?.getWord().toLowerCase();
  if (!word || !gameStore.gameState) {
    return;
  }

  const { word: target, rows, topicId } = gameStore.gameState;
  if (word !== target) {
    // if the target itself is not inside the dict, no need to check if it's a valid word, fix #227
    if (store.dict.indexOf(target) !== -1 && store.dict.indexOf(word) === -1) {
      let { langName } = store;
      langName = langName.replace(/\(\w+\)/, '') || 'English';
      const a = /^[aeio]/i.test(langName) ? 'an' : 'a';
      grid.value?.shakeWrongWord();
      onMessage(`Not ${a} ${langName} word`);
      return;
    }
  }

  const record:RowItem = [];
  const taken: Record<string, boolean[]> = {};
  let total = 0;
  for (let i = 0, len = word.length; i < len; i++) {
    const letter = word.charAt(i);
    const cell:CellItem = {
      letter,
      score: 0,
    };
    // target undefined, not sure how to happen
    console.log('target:', target);
    console.log('gameState:', gameStore.gameState);
    if (target.charAt(i) === letter) {
      cell.score = 10;
    } else if (target.indexOf(letter) !== -1) {
      let index = target.indexOf(letter);
      let isAllMatched = true;
      const theTaken = taken[ letter ] || [];
      while (index !== -1) {
        const char = word.charAt(index);
        isAllMatched = (char === letter || theTaken[ index ]) && isAllMatched;
        if (!isAllMatched) {
          theTaken[ index ] = true;
          break;
        }
        index = target.indexOf(letter, index + 1);
      }
      cell.score = isAllMatched ? 0 : 1;
      taken[ letter ] = theTaken;
    }
    total += cell.score;
    record.push(cell);
  }
  gameStore.addRowRecord(record);
  if (total >= store.letterNumber * 10) {
    onMessage('You win!');
    hasConfetti.value = true;
    gameStore.addHistory({
      win: true,
      level: rows.length,
    });
    if (store.isCustom && topicId) {
      store.setCustomProgress({
        word: target,
        topicId
      });
    }
  } else if (rows.length >= store.maxStep) {
    onMessage('You lose.');
    gameStore.addHistory({ win: false, level: rows.length });
  }
  if (header.value && (total >= store.letterNumber * 10 || rows.length >= store.maxStep)) {
    setTimeout(() => {
      if (header.value) (header.value as InstanceType<typeof AppHeader>).doOpenModal();
    }, 1000);
  }
}

function onDelete() {
  if (gameStore.gameState?.done) {
    return;
  }
  grid.value?.deleteKey();
}

let messageTimeout:Timeout;
function onMessage(str:string) {
  clearTimeout(messageTimeout);
  message.value = str;
  messageTimeout = setTimeout(() => {
    message.value = '';
  }, 1250);
}

onMounted(async () => {
  await initialLoading();
  store.createInitialGame();
  message.value = 'Please Enjoy';
  await sleep(500);
  message.value = '';
});
</script>

<template>
  <app-header ref="header" @message="onMessage">
    <template v-if="store.fixedTopic" #fixed>
      <fixed-topic/>
    </template>
    <app-heading />
  </app-header>
  <div class="z-20">
    <aw-notification :message="message" class="text-2xl" />
  </div>
  <div class="aw-container">
    <game-grid ref="grid"/>
  </div>
  <aw-keyboard @delete="onDelete" @enter="onEnter" @keypress="onKeypress"/>
  <top10-list/>
  <client-only>
    <confetti v-if="hasConfetti" />
  </client-only>
</template>
