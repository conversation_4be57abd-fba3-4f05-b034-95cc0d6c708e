<script setup lang="ts">
import Languages from '~/data/languages';
</script>

<template>
  <footer class="color-bg">
    <div id="languages" class="pt-4 pb-10 mx-auto aw-container">
      <h2 class="mb-2 text-center text-lg">Play wordle unlimited in other languages:</h2>
      <div id="languages-list" class="grid grid-cols-3 xs:grid-cols-2">
        <nuxt-link
          v-for="(lang, key) in Languages"
          :key="key"
          :to="key === 'English(all)' ? '/' : `/wordle-${name2slug(key)}`"
          :title="`Wordle ${key}`"
          class="rounded px-3 py-2"
        >{{lang.flag}}&nbsp; {{key}}</nuxt-link>
      </div>
    </div>
  </footer>
</template>
