<script lang="ts" setup>
import { PinIcon, XIcon } from 'lucide-vue-next';
import type { TopicItem } from '~/types';
import useStore from '~/store';
import useGameStore from '~/store/game';

type Props = {
  isFixed?: boolean;
  showTitle?: string;
  topicId: number;
  words: TopicItem[];
}
const props = withDefaults(defineProps<Props>(), {
  isFixed: false,
  showTitle: '',
});

const store = useStore();
const gameStore = useGameStore();
const route = useRoute();
const root = ref<HTMLDivElement>();
const inner = ref<HTMLDivElement>();
const currentTopicId = computed<number>(() => {
  return Number(route.query.tid);
});
const currentTopicWord = computed<string>(() => {
  return route.query.w as string;
});
const progress = computed<string[]>(() => {
  return store.topicProgress;
});

function doClose() {
  store.setConfig({ fixedTopic: 0 });
}

function doFix(topicId: number) {
  store.setConfig({ fixedTopic: topicId });
}

function onClickLink(event: MouseEvent, word: string, navigate: (event: MouseEvent) => void) {
  const game = gameStore.gameState;
  if (!game) return;
  if (!game.done && game.rows.length > 0
    && word !== game.word
    && !confirm('Switching to other word will lose current game progress, are you sure?')
  ) {
    event.preventDefault();
    return;
  }

  navigate(event);
  doFix(props.topicId);
}

function scrollIntoView() {
  root.value?.scrollIntoView({
    block: 'center',
    inline: 'center',
    behavior: 'smooth',
  });
}

defineExpose({
  scrollIntoView,
  inner,
});
</script>

<template>
<div
  ref="root"
  class="theme-item border rounded"
>
  <header
    v-if="isFixed"
    class="border-b flex justify-between items-center"
  >
    <slot>
      <h3 :title="showTitle">{{showTitle}}</h3>
    </slot>
    <button
      class="border-0 w-8 h-8 m-1"
      type="button"
      aria-label="Close"
      @click="doClose"
    >
      <x-icon class="size-4" />
    </button>
  </header>
  <header
    v-else
    class="border-b flex justify-between px-2 leading-8"
  >
    <div class="w-8" />
    <h3
      class="font-bold text-xl leading-loose2-5 whitespace-nowrap overflow-hidden text-ellipsis"
      :title="showTitle"
    >{{showTitle}}</h3>
    <button
      class="w-8 text-key-color fix-topic-button"
      type="button"
      aria-label="Fix to top"
      @click="doFix(topicId)"
    >
      <pin-icon class="size-4" />
    </button>
  </header>
  <div
    ref="inner"
    class="topic-item-inner font-mono"
    :class="isFixed ? 'max-h-60 overflow-auto' : 'grid grid-cols-3 p-2 xs:grid-cols-2'"
  >
    <nuxt-link
      v-for="(item, index) in words"
      :key="item.url"
      v-slot="{ href, navigate }"
      :to="item.url"
      custom
    >
      <a
        class="pl-2 py-2 uppercase truncate block text-sm hover:bg-light-bg"
        :class="{
          'success-word': progress.includes(`${topicId}-${item.word}`),
          'bg-light-bg': currentTopicId === topicId && item.hashed === currentTopicWord
        }"
        :href="href"
        @click="onClickLink($event, item.word, navigate)"
      >
        <span
          class="inline-block mr-2 w-6 text-center"
          :class="{'color-bg rounded': index >= 3}"
        >{{item.medal}}</span>
        {{progress.includes(`${topicId}-${item.word}`) ? item.word : item.placeholder}}
      </a>
    </nuxt-link>
  </div>
</div>
</template>
