<template>
<div class="switch">
  <input
    :id="localId"
    v-model="localValue"
    type="checkbox"
    hidden="hidden"
    @change="onChange"
  >
  <label class="switch-wrapper" :for="localId" />
</div>
</template>

<script lang="ts" setup>
type Props = {
  valueOn?: boolean;
  valueOff?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  valueOn: true,
  valueOff: false,
});
const modelValue = defineModel<boolean>('modelValue');

const localId = `switch-${useId()}`;
const localValue = ref<boolean>(modelValue.value === props.valueOn);

function onChange(event:Event) {
  const { target } = event;
  const { checked } = target as HTMLInputElement;
  modelValue.value = checked ? props.valueOn : props.valueOff;
}
function reset() {
  localValue.value = modelValue.value === props.valueOn;
}

defineExpose({
  reset,
});
</script>
