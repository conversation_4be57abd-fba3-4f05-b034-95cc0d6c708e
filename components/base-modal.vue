<script lang="ts" setup>
import { XIcon } from 'lucide-vue-next';

type Props = {
  class?: string;
}
const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'close'):void;
}>();

const root = ref<HTMLDialogElement>();

onMounted(() => {
  root.value?.showModal();
});
</script>

<template>
<teleport to="body">
  <dialog
    ref="root"
    class="modal"
    @close="emit('close')"
  >
    <div
      class="modal-box relative bg-white dark:bg-gray-900"
      :class="props.class"
    >
      <form method="dialog" class="absolute top-4 right-4">
        <button
          class="btn btn-ghost btn-sm btn-circle focus-visible:outline-none"
          aria-label="Close"
        >
          <x-icon class="w-6" />
        </button>
      </form>
      <slot />
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>close</button>
    </form>
  </dialog>
</teleport>
</template>
