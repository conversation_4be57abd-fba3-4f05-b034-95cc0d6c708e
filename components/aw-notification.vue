<script lang="ts" setup>
type Props = {
  class?: string;
  parent?: string | HTMLElement | null;
  message?: string;
  status?: boolean | null;
}
const props = withDefaults(defineProps<Props>(), {
  class: '',
  message: '',
  parent: 'body',
  status: null,
});
</script>

<template>
<teleport
  :to="parent"
>
  <transition
    name="error-transition"
    enter-active-class="animated fadeIn"
    leave-active-class="animated fadeOut"
  >
    <div
      v-if="message"
      class="notification px-4 py-2 w-fit fixed text-white mx-auto inset-x-0 z-20"
      :class="[
        {
          'bg-gray-700': status === null,
          'bg-green-400': status === true,
          'bg-red-500': status === false,
        },
        props.class,
      ]"
    >{{message}}</div>
  </transition>
</teleport>
</template>
