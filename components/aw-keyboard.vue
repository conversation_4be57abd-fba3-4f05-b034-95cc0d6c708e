<script lang="ts" setup>
import { DeleteIcon } from 'lucide-vue-next';
import languages from '@/data/languages';
import KeyboardLayouts from '@/data/keyboard-layouts';
import useGameStore from '~/store/game';
import useStore from '~/store';

const store = useStore();
const gameStore = useGameStore();

const emit = defineEmits<{
  (e: 'keypress', key: string): void;
  (e: 'enter' | 'delete'):void;
}>();

type Key = {
  letter: string;
  score: number;
  order: number;
};

const KEYS = computed<string[]>(() => {
  if (store.langName && languages[ store.langName ]) {
    const { short } = languages[ store.langName ];
    return KeyboardLayouts[ short ] || KeyboardLayouts.en;
  }
  return KeyboardLayouts.en;
});
const keyboard = computed<Key[]>(() => {
  const { letterScore } = gameStore;
  return KEYS.value.reduce((keys, row:string, index) => {
    index = index >= 3 ? index + 3
        : (index > 1 ? index + 2 : index + 1);
    const rowKeys:Key[] = row.split('').map(letter => {
      const score = letterScore[ letter ] ?? -1;
      return {
        letter,
        score,
        order: index,
      };
    });
    keys.push(...rowKeys);
    return keys;
  }, [] as Key[]);
});
const keyboardDisabled = computed(() => store.isKeyboardDisabled);
const maxRow = computed(() => {
  return KEYS.value.reduce((max, row) => {
    return Math.max(max, row.length);
  }, 0);
})
const charRegex = computed<RegExp>(() => {
  if (!store.langName || /^en/i.test(store.langName)) {
    return /^[a-z]$/i;
  }
  if (/^ru/i.test(store.langName)) {
    return /^\p{sc=Cyrillic}$/u;
  }
  if (/^greek$/i.test(store.langName)) {
    return /^\p{sc=Greek}$/u;
  }
  return /^\p{sc=Latin}$/u;
});
const langSlug = computed<string>(() => store.langSlug || 'english');
const isHighContrast = computed<boolean>(() => store.config.highContrast);
const isFixed = ref<boolean>(false);
const root = ref<HTMLDivElement>();

function onClick(event:MouseEvent) {
  const { target } = event;
  if ((target as HTMLDivElement).classList.contains('keyboard')) {
    return;
  }
  const key = String((target as HTMLDivElement).textContent);
  switch (key) {
    case 'enter': emit(key); break;
    case 'delete': emit(key); break;

    default:
      emit('keypress', key);
  }
}
function onKeydown(event:KeyboardEvent) {
  if (keyboardDisabled.value || document.body.classList.contains('modal-open')) {
    return;
  }

  const { key } = event;
  if (key === 'Enter') {
    // event.preventDefault();
    emit('enter');
    return;
  }
  if (key === 'Backspace') {
    // event.preventDefault();
    emit('delete');
    return;
  }
  if (charRegex.value.test(key)) {
    // event.preventDefault();
    document.getElementById('key-' + key)?.classList.add('active');
    emit('keypress', key);
    return;
  }
}
function onTransitionEnd(event:TransitionEvent) {
  (event.target as HTMLElement).classList.remove('active');
}

onMounted(() => {
  document.addEventListener('keydown', onKeydown);
  const rect = root.value?.getBoundingClientRect();
  if (rect && window.innerHeight - rect.top < rect.height * .5) {
    isFixed.value = true;
  }
});
onBeforeUnmount(() => {
  document.removeEventListener('keydown', onKeydown);
});
</script>

<template>
<div
  ref="root"
  class="keyboard flex flex-wrap px-2 justify-center bg-white aw-container"
  :class="[langSlug, 'letter-' + maxRow, {'fixed-bottom': isFixed, 'high-contrast': isHighContrast}]"
  @click="onClick"
  @transitionend="onTransitionEnd"
>
  <button
    v-for="key in keyboard"
    :id="'key-' + key.letter"
    :key="key.letter"
    class="rounded keyboard-item font-semibold"
    :class="[{'maybe': key.score === 1, 'win': key.score > 1, 'muted': key.score === 0}, 'order-' + key.order]"
    type="button"
  >
    {{ key.letter }}
  </button>
  <button class="rounded keyboard-item keyboard-item-enter order-3" type="button">enter</button>
  <button class="rounded keyboard-item keyboard-item-delete order-5" type="button">
    <span class="sr-only">delete</span>
    <delete-icon class="size-6" />
  </button>
</div>
</template>
