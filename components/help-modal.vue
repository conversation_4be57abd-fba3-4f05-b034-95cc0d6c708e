<script lang="ts" setup>
import useStore from "~/store";

const store = useStore();
</script>
<template>
<div>
  <h2 class="text-lg mb-2 font-semibold">Guess the word in {{store.letterNumber + 1}} tries.</h2>
  <p class="text-sm">After each guess, the color of the tiles will change to show how close your guess was to the word.</p>
  <hr class="my-4">
  <div class="flex mb-4 pl-4 font-bold text-2xl font-mono">
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item win dark:bg-green-600">T</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">A</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">B</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">L</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">E</div>
  </div>
  <p class="text-sm mb-6 pl-4">The letter T is in the word and in the correct spot.</p>
  <div class="flex mb-4 pl-4 font-bold text-2xl font-mono">
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 dark:border-gray-700">P</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">A</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item maybe ml-2 dark:bg-orange-600">Y</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">E</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">R</div>
  </div>
  <p class="text-sm mb-6 pl-4">The letter Y is in the word but in the wrong spot.</p>
  <div class="flex mb-4 pl-4 font-bold text-2xl font-mono">
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 dark:border-gray-700">O</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">T</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">H</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item border-2 ml-2 dark:border-gray-700">E</div>
    <div class="rounded-lg flex justify-center items-center w-12 h-12 game-item muted ml-2 dark:bg-gray-600">R</div>
  </div>
  <p class="text-sm mb-6 pl-4">The letter R is not in the word in any spot.</p>
</div>
</template>
