<script setup lang="ts">
import { CheckIcon, LinkIcon } from 'lucide-vue-next';
import { copyTextToClipboard } from '~/utils';

type Props = {
  buttonClass?: string;
  shareUrl: string;
  urlClass?: string;
}
const props = defineProps<Props>();

let timeout:Timeout;
const isCopied = ref<boolean>(false);

function onClickLink(event:MouseEvent) {
  if (!props.shareUrl) {
    event.stopPropagation();
    event.preventDefault();
    return false;
  }
}
async function doCopyLink() {
  clearTimeout(timeout);
  isCopied.value = true;
  await copyTextToClipboard(props.shareUrl);
  timeout = setTimeout(() => {
    isCopied.value = false;
  }, 1250);
}
</script>

<template>
  <a
    v-if="shareUrl"
    class="w-full block text-xs break-all dark:text-blue-300 dark:hover:text-blue-200 hover:text-blue-600"
    :class="urlClass"
    :href="shareUrl"
    target="_black"
  >{{shareUrl}}</a>
  <footer
    class="flex justify-center"
    :class="buttonClass"
  >
    <a
      class="rounded-full w-12 h-12 bg-twitter flex justify-center items-center text-white text-xl hover:bg-blue-500"
      :class="{'disabled': !shareUrl}"
      target="_blank"
      rel="noreferrer"
      title="Share to Twitter"
      :href="'https://twitter.com/intent/tweet?url=' + encodeURIComponent(shareUrl)"
      @click="onClickLink"
    >
      <i class="bi bi-twitter" />
    </a>
    <a
      class="rounded-full w-12 h-12 bg-facebook flex justify-center items-center text-white text-xl ml-2 hover:bg-blue-700"
      :class="{'disabled': !shareUrl}"
      target="_blank"
      rel="noreferrer"
      title="Share to Facebook"
      :href="'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(shareUrl)"
      @click="onClickLink"
    >
      <i class="bi bi-facebook" />
    </a>
    <button
      aria-label="Copy to clipboard"
      class="rounded-full w-12 h-12 flex justify-center items-center text-white text-xl ml-2"
      :class="isCopied ? 'bg-green-500 hover:bg-green-400' : 'bg-gray-400 hover:bg-gray-300'"
      :disabled="!shareUrl"
      type="button"
      @click="doCopyLink"
    >
      <check-icon v-if="isCopied" class="size-4" />
      <link-icon v-else class="size-4" />
    </button>
  </footer>
</template>
