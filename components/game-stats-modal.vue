<template>
<div>
  <h2 class="text-lg font-semibold text-center uppercase mb-1">Statistics</h2>
  <div class="stat-items flex justify-center mb-7 mx-4">
    <div class="stat-item text-center w-2/12">
      <div class="stat-number font-bold">{{history.played}}</div>
      <div class="stat-label text-xs leading-none font-semibold">Played</div>
    </div>
    <div class="stat-item text-center w-2/12">
      <div class="stat-number font-bold">{{history.played ? Math.round(history.win / history.played * 10000) / 100 : 0}}</div>
      <div class="stat-label text-xs leading-none font-semibold">Win %</div>
    </div>
    <div class="stat-item text-center w-2/12">
      <div class="stat-number font-bold">{{history.streak}}</div>
      <div class="stat-label text-xs leading-none font-semibold">Current<br>Streak</div>
    </div>
    <div class="stat-item text-center w-2/12">
      <div class="stat-number font-bold">{{history.maxStreak}}</div>
      <div class="stat-label text-xs leading-none font-semibold">Max<br>Streak</div>
    </div>
  </div>
  <h2 class="text-lg text-center uppercase font-semibold">Guess distribution</h2>
  <dl class="flex flex-wrap items-center px-9 mb-3">
    <template
      v-for="(item, index) in history.stats"
      :key="index"
    >
      <dt class="w-4 text-sm">{{index + 1}}</dt>
      <dd class="w-m-4">
        <progress :value="item" :max="max">{{item}}</progress>
      </dd>
    </template>
  </dl>
</div>
</template>

<script lang="ts" setup>
import type { History } from '~/types';
import useStore from '~/store';

const store = useStore();

const history = computed<History>(() => store.history);
const max = computed(() => {
  return Math.max(...store.history.stats);
});
</script>
