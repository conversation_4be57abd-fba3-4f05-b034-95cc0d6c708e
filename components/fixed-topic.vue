<script lang="ts" setup>
import { TopicItem as TopicItemComp } from '#components';
import { sleep } from '@/utils';
import useStore from '~/store';
import type { Topic, TopicItem } from '~/types';

type ShowTitle = {
  title?: string;
  success?: number;
  total?: number;
}

const store = useStore();
const route = useRoute();

const isFlash = ref<boolean>(false);
const isOpen = ref<boolean>(false);
const root = ref<InstanceType<typeof TopicItemComp>>();
const title = ref<HTMLElement>();
const inner = ref<HTMLDivElement>();

const topicId = computed<number>(() => store.config.fixedTopic || 0);
const topic = computed<Topic | null>(() => {
  return store.fixedTopic;
});
const words = computed<TopicItem[]>(() => {
  return topic.value?.topicItems || [];
});
const currentTopicWord = computed<string>(() => {
  return route.query.w as string;
});

const showTitle = computed<ShowTitle>(() => {
  if (!topic.value) {
    return {} as ShowTitle;
  }
  const success: number = words.value.reduce((acc, item) => {
    return acc + (store.topicProgress.includes(`${store.config.fixedTopic}-${item.word}`) ? 1 : 0);
  }, 0);
  return {
    title: topic.value.title || '',
    success,
    total: words.value.length,
  };
});

watch(topicId, () => {
  onChange();
});
watch(currentTopicWord, async (newValue, oldValue) => {
  if (oldValue && newValue !== oldValue) {
    await nextTick();
    scrollToSelected();
  }
});

onMounted(() => {
  onChange();
  inner.value = root.value?.$refs.inner as HTMLDivElement;
  scrollToSelected();
});

async function onChange() {
  root.value?.scrollIntoView();
  await sleep(500);
  isFlash.value = true;
}

function onClick(event: MouseEvent) {
  const { target } = event;
  if (title.value?.contains(target as Element)) {
    isOpen.value = !isOpen.value;
  } else if (inner.value?.contains(target as Element)) {
    isOpen.value = false;
  }
}

function scrollToSelected(): void {
  const currentWord = inner.value?.querySelector('.bg-light-bg');
  currentWord?.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
    inline: 'center',
  });
}
</script>

<template>
  <topic-item-comp
    ref="root"
    class="fixed-topic absolute"
    is-fixed
    :topic-id="topicId"
    :words="words"
    :class="{'animated flash': isFlash, 'open': isOpen}"
    @animationend="isFlash = false"
    @click="onClick"
  >
    <h3
      ref="title"
      class="text-sm flex items-center"
      :title="`${showTitle.title} ${showTitle.success}/${showTitle.total}`"
    >
      <span class="font-bold py-1 leading-tight">{{showTitle.title}}</span>
      <span class="ml-2 success-word">{{showTitle.success}}</span>
      <span class="font-light">/{{showTitle.total}}</span>
    </h3>
  </topic-item-comp>
</template>
