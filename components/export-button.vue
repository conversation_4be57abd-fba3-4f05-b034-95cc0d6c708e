<template>
<a
  class="inline-block"
  href="/mywordgame.json"
  @click="doExportData"
>
  <slot>Export</slot>
  <aw-notification
    :status="status"
    :message="message"
  />
</a>
</template>

<script lang="ts" setup>
const message = ref<string | null>();
const status = ref<boolean | null>();

function doExportData(event:MouseEvent) {
  // FIXME: this should be a JSON file
  const json = JSON.stringify({});
  const blob = new Blob([json], { type : 'application/json' });
  const url = URL.createObjectURL(blob);
  const target = event.target as HTMLAnchorElement;
  target.href = url;
  target.download = 'mywordgame.json';
  status.value = true;
  message.value = 'Exported successfully. Your data download will begin automatically.';
  setTimeout(() => {
    status.value = null;
    message.value = null;
  }, 2500);
}
</script>
