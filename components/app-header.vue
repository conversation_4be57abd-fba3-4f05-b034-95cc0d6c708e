<template>
<header id="header">
  <slot name="fixed" />
  <div class="inner flex">
    <button
      type="button"
      aria-label="Help"
      title="Help"
      tabindex="-1"
      @click="(event: MouseEvent) => handleButtonClick(Modal.Help, event)"
    >
      <span class="icon flex items-center justify-center">
        <circle-help-icon class="size-4" />
      </span>
      <span class="sr-only">Help</span>
    </button>
    <button
      type="button"
      aria-label="Statistics"
      title="Statistics"
      tabindex="-1"
      @click="(event: MouseEvent) => handleButtonClick(Modal.Stats, event)"
    >
      <span class="icon flex items-center justify-center">
        <chart-no-axes-column-increasing class="size-4" />
      </span>
      <span class="sr-only">Statistics</span>
    </button>
    <slot>
      <h1>Wordle</h1>
    </slot>
    <button
      type="button"
      aria-label="Customize Wordle"
      title="Customize Wordle"
      tabindex="-1"
      @click="(event: MouseEvent) => handleButtonClick(Modal.Share, event)"
    >
      <span class="icon flex items-center justify-center">
        <share2-icon class="size-4" />
      </span>
      <span class="sr-only">Customize</span>
    </button>
    <button
      type="button"
      aria-label="Config"
      title="Config"
      tabindex="-1"
      @click="(event: MouseEvent) => handleButtonClick(Modal.GameConfig, event)"
    >
      <span class="icon flex items-center justify-center">
        <settings-icon class="size-4" />
      </span>
      <span class="sr-only">Config</span>
    </button>
  </div>
</header>
<base-modal
  v-if="showModal"
  :class="[showModal, {'high-contrast': isHighContrast}]"
  @close="onClose"
>
  <component
    :is="modalComponent"
    v-if="modalComponent"
    :is-welcome="isWelcome"
    @close="onClose"
    @message="onMessage"
  />
  <div
    v-else
    class="h-16 flex items-center justify-center"
  >
    <span class="loading loading-spinner" />
  </div>
</base-modal>
</template>

<script lang="ts" setup>
import { ChartNoAxesColumnIncreasing, CircleHelpIcon, SettingsIcon, Share2Icon } from 'lucide-vue-next';
import type { EsModule } from '@/types';
import useStore from '~/store';
import useGameStore from '~/store/game';

enum Modal {
  Help = 'help',
  Share = 'share',
  GameConfig = 'game-config',
  Stats = 'game-stats',
}

const isWelcome = ref<boolean>(false);
const showModal = ref<string | null>(null);
const modalComponent = shallowRef();

const emit = defineEmits<{
  (e:'message', message:string): void;
}>();
const store = useStore();
const gameStore = useGameStore();

const isHighContrast = computed(() => store.config.highContrast);

function onClose(letter?:number) {
  showModal.value = null;
  isWelcome.value = false;
  modalComponent.value = null;
  if (letter) {
    setTimeout(() => {
      isWelcome.value = true;
      doOpenModal();
    }, 300);
  }
}
function onMessage(message:string) {
  emit('message', message);
}

const allModals = import.meta.glob('./*-modal.vue');
async function doOpenModal(type: Modal = Modal.Share) {
  showModal.value = type;
  const component = (await allModals[ `./${type}-modal.vue` ]()) as EsModule<HTMLDivElement>;
  modalComponent.value = component.default;
}

function handleButtonClick(type: Modal, event: MouseEvent) {
  doOpenModal(type);
  const target = event.target as HTMLElement;
  target.blur(); // remove focus
  nextTick(() => {
    // and focus body
    document.body.focus();
  });
}

onMounted(() => {
  // in Safari, animation will cause elements at the top of the viewport
  // which looks weird. So we need to wait for the last animation to finish.
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  const delay = isSafari ? (store.letterNumber - 1) * 125 + 400 : 600;
  // for issue #21
  if (store.isSpecifiedWord && !store.masked && !store.fixedTopic) {
    setTimeout(() => {
      doOpenModal();
    }, delay);
    isWelcome.value = true;
    return;
  }

  // for issue #11
  if (gameStore.gameState?.done) {
    setTimeout(() => {
      doOpenModal();
    }, delay);
  }
});

defineExpose({
  doOpenModal,
  showModal,
});
</script>
