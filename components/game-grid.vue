<script lang="ts" setup>
import type { CellItem } from '@/types';
import { sleep } from '@/utils';
import useGameStore from '~/store/game';
import useStore from '~/store';

const store = useStore();
const gameStore = useGameStore();

const isShake = ref<boolean>(false);
const current = ref<number>(0);
const originalItems = ref<string[]>([]);

const checkedItems = computed<CellItem[]>(() => {
  return gameStore.gameState?.rows.flat() || [];
});
const gameItems = computed<string[]>(() => {
  return originalItems.value.slice(gameStore.level * store.letterNumber);
});
const className = computed<string[]>(() => {
  const classes = ['grid-cols-' + store.letterNumber];
  if (store.letterNumber < 8) {
    classes.push(`w-${store.letterNumber + 4}/12 gap-2.5`);
  } else {
    classes.push('w-full');
    if (store.letterNumber < 11) {
      classes.push('px-2 gap-1');
    } else {
      classes.push('gap-y-1');
    }
  }
  if (store.config.highContrast) {
    classes.push('high-contrast');
  }
  return classes;
});

function reset() {
  originalItems.value = new Array(store.letterNumber * store.maxStep).fill('');
  current.value = gameStore.level * store.letterNumber;
}
function pressKey(key: string) {
  console.log(1)
  if (current.value >= store.letterNumber * (gameStore.level + 1)) {
    return;
  }
  console.log(2)
  originalItems.value[ current.value ] = key;
  current.value += 1;
}
function deleteKey() {
  if (current.value <= store.letterNumber * gameStore.level) {
    return;
  }
  originalItems.value[ current.value - 1 ] = '';
  current.value -= 1;
}
function getWord():string {
  if (current.value === 0 || current.value % store.letterNumber !== 0) {
    return '';
  }
  const pos = gameStore.level * store.letterNumber;
  return originalItems.value.slice(pos, pos + store.letterNumber).join('');
}
async function shakeWrongWord() {
  isShake.value = true;
  await sleep(200);
  isShake.value = false;
}

function onAnimationEnd(event: AnimationEvent) {
  const target = event.target as HTMLElement;
  const animationName = event.animationName;
  if (animationName === 'pulse') {
    target.classList.remove(animationName);
  } else if (animationName === 'flip') {
    target.removeChild(target.firstElementChild!);
  }
}

reset();

watch(() => gameStore.gameState, (value) => {
  if (!value) return;
  reset();
});

defineExpose({
  reset,
  pressKey,
  deleteKey,
  getWord,
  shakeWrongWord,
});
</script>

<template>
<div
  class="game-grid grid m-auto py-8"
  :class="className"
  @animationend="onAnimationEnd"
>
  <div
    v-for="(item, index) in checkedItems"
    :key="'checkItem' + index"
    class="game-item text-2xl font-mono font-bold animated faster flip"
    :class="`delay-${index % store.letterNumber}`"
  >
    <div
      class="face back"
      :class="store.letterNumber > 9 ? 'rounded-md border' : 'rounded-lg border-2'"
    >{{item.letter}}</div>
    <div
      class="face"
      :class="[
        {
          'muted': item.score === 0,
          'maybe': item.score === 1,
          'win': item.score > 1
        },
        store.letterNumber > 9 ? 'rounded-md' : 'rounded-lg'
      ]"
    >{{item.letter}}</div>
  </div>
  <div
    v-for="(item, index) in gameItems"
    :key="'gridItem' + index"
    class="game-item flex justify-center items-center text-2xl font-mono font-bold"
    :class="[
      {'animated faster2 pulse active': item, 'shakeX': isShake},
      store.letterNumber > 9 ? 'rounded-md' : 'rounded-lg',
      item ? (store.letterNumber > 9 ? 'border': 'border-2') : '',
      `z-[${gameItems.length - index}]`
    ]"
  >{{item}}</div>
</div>
</template>
