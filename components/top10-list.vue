<script setup lang="ts">
import type { TopicCate } from '~/types';
import { parseSearch } from '@/utils/custom-link';
import useStore from '~/store';

const CUSTOM = '➕ Custom';
// custom creative shared
const { isShared, topicName } = parseSearch();
const store = useStore();

const { data } = useAsyncData<TopicCate[]>(
  'topics',
  async function () {
    return await store.loadTopics();
  },
  {
    default() {
      return [];
    },
  },
);

const cates = computed<string[]>(() => {
  const result = [...data.value.map(item => item.category), CUSTOM];
  if (isShared) {
    result.unshift(topicName);
  }
  return result;
});
const activeCate = computed({
  get: () => store.config.currentCateName,
  set: active => store.setConfig({ currentCateName: active })
});

watch(data, value => {
  const needDefaultCate = isShared
    || !activeCate.value
    || !value.some((group: TopicCate) => group.category === activeCate.value);
  if (needDefaultCate && value[ 0 ]) {
    store.setConfig({ currentCateName: topicName || value[ 0 ].category });
  }
});
</script>

<template>
  <aw-tabs
    v-if="cates.length > 1"
    v-model="activeCate"
    :list="cates"
  />
  <div class="color-bg">
    <div
      v-if="store.theTopics && activeCate !== CUSTOM"
      id="themes-list"
      class="pt-4 pb-2 container mx-auto grid gap-4 lg:grid-cols-2 xl:grid-cols-3"
    >
      <topic-item
        v-for="(topic, title) in store.theTopics"
        :key="title"
        :show-title="title"
        :words="topic.topicItems"
        :topic-id="topic.topicId"
      />
    </div>
    <div
      v-show="activeCate === CUSTOM"
      class="pt-4 pb-2"
    >
      <aw-custom/>
    </div>
  </div>
</template>
