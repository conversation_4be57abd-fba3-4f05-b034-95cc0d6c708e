/// <reference types="vite/client" />

declare interface Window {
  dataLayer: IArguments[];
  ga: (message: string) => void;
  _rsbtxt: string[];
  prestige: string;
  og_result_img: string;
  og_result_url: string;
  og_link: string;
  test: {
    name: string;
    lang: string;
    url: string;
  };
}

declare const __IS_DEV__: boolean;
declare const __VERSION__: string;
declare const ga: (method:string, key?:string, options?:string) => void
