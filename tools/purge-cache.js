const https = require('https');
const { readFile } = require('fs/promises');
const { resolve } = require('path');

(async () => {
  // server.txt
  // URL
  // email
  // key
  const config = await readFile(resolve(__dirname, './purge-cache.txt'), 'utf8');
  const [url, email, key] = config.split('\n');
  const req = https.request(url, {
    method: 'POST',
    headers: {
      'X-Auth-Email': email,
      'X-Auth-Key': key,
      'Content-Type': 'application/json',
    },
  }, res => {
    res.setEncoding('utf8');
    res.on('data', chunk => {
      console.log(chunk);
    });
    res.on('end', () => {
      console.log('response end');
    });
  });

  req.on('error', (e) => {
    console.error(e);
  });

  req.write('{"purge_everything":true}');
  req.end();
})();
