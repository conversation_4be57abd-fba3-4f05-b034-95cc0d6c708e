const {
  readFile,
  writeFile,
} = require('fs/promises');
const { promisify } = require('util');
const glob = promisify(require('glob'));
const { resolve } = require('path');
const basePath = resolve(__dirname, '../public');
const { shuffle } = require('lodash');

(async () => {
  const frequency = await glob(basePath + '/f*.txt');
  const more = await glob(basePath + '/more/**/*.txt');
  const files = [...frequency, ...more];
  for (const file of files) {
    const content = await readFile(file, 'utf8');
    const words = content.split(',');
    const shuffled = shuffle(words);
    await writeFile(file, shuffled.join(','));
    console.log(file + ' shuffled');
  }
  console.log('done.');
})()
