const { readFile, writeFile } = require('node:fs/promises');
const { glob } = require('glob');

(async function() {
  const dict = await readFile('./combined_words_top_4000.txt', 'utf-8');
  const words = dict.split('\n').map((word) => word.trim()).filter(Boolean);

  const files = await glob('../public/f*.txt');
  for (const file of files) {
    const content = await readFile(file, 'utf-8');
    const lines = content.split(',');
    const newLines = lines.filter((line) => {
      return words.includes(line);
    });
    await writeFile(file, newLines.join(','));
    console.log(`Cleaned ${file}`);
  }

  console.log('Done');
})();
