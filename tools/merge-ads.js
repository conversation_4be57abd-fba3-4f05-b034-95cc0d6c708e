#!/usr/bin/env node

const { readFile, writeFile } = require('fs/promises');
const { resolve } = require('path');
const { promisify } = require('util');
const glob = promisify(require('glob'));
const domain = process.env.DOMAIN ? process.env.DOMAIN.replace(/^https?:\/\//, '') : 'mywordle.org';

(async () => {
  const publicPath = resolve(__dirname, '../public');
  const adSource = resolve(__dirname, '../src/ad')
  const distPath = resolve(__dirname, '../dist');
  const adTxt = await glob(adSource + '/*.ads.txt');
  const moreAdTxt = await glob(adSource + `/${domain}/*.ads.txt`);
  const ads = new Set();
  for (const file of [...adTxt, ...moreAdTxt]) {
    const content = await readFile(file, 'utf8');
    const lines = content.split('\n');
    for (let line of lines) {
      line = line.trim();
      if (line.startsWith('#') || !line) {
        continue;
      }
      ads.add(line);
    }
  }

  const content = [...ads].join('\n');
  await writeFile(publicPath + `/ads.${domain}.txt`, content, 'utf8');
  await writeFile(distPath + `/ads.${domain}.txt`, content, 'utf8');
})();
