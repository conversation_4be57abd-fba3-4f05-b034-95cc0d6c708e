const { readFile, writeFile } = require('fs/promises');
const { resolve } = require('path');
const { promisify } = require('util');
const glob = promisify(require('glob'));
const basePath = resolve(__dirname, '../public');

(async () => {
  const content = await readFile(resolve(__dirname, 'bad-words.txt'), 'utf8');
  const badWords = content.split('\n')
    .reduce((acc, word) => {
      if (word.length > 11 || word.length < 4) {
        return acc;
      }
      acc[ word.length ] = acc[ word.length ] || [];
      acc[ word.length ].push(word);
      return acc;
    }, {});
  const frequency = await glob(basePath + '/f*.txt');
  for (const file of frequency) {
    const content = await readFile(file, 'utf8');
    const words = content.split(',');
    const match = file.match(/f(\d+).txt$/);
    const bad = badWords[ match[ 1 ] ];
    const filtered = words.filter(word => !bad.includes(word));
    await writeFile(file, filtered.join(','));
  }
})();
