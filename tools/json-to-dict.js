const {
  readFile,
  writeFile,
  mkdir,
} = require('fs/promises');
const { existsSync } = require('fs');
const { resolve } = require('path');
const { shuffle } = require('lodash');

const MIN_LETTER_NUMBER = 4;
const MAX_LETTER_NUMBER = 11;
const range = MAX_LETTER_NUMBER - MIN_LETTER_NUMBER + 1;

function getRegex(lang) {
  if (!lang || /^en/i.test(lang)) {
    return /^[a-z]+$/i;
  }
  if (lang === 'ru' || lang === 'uk') {
    return /^\p{sc=Cyrillic}+$/u;
  }
  if (lang === 'el') {
    return /^\p{sc=Greek}+$/u;
  }
  return /^\p{sc=Latin}+$/u;
}

(async () => {
  //const languages = await loadTs(path);
  const languages = {
    'Ukrainian': {
      short: 'uk',
    },
  };
  for (const language in languages) {
    const { short } =  languages[ language ];
    if (/^en/.test(short)) {
      continue;
    }

    let allWords;
    if (existsSync(resolve(__dirname, `./${short}.json`))) {
      allWords = require(`./${short}.json`);
    } else if (existsSync(resolve(__dirname, `./${short}.txt`))) {
      allWords = await readFile(resolve(__dirname, `./${short}.txt`), 'utf8');
      allWords = allWords.split('\n');
    }
    const regex = getRegex(short);
    const dicts = allWords.reduce((memo, word) => {
      const length = word.length;
      if (length < MIN_LETTER_NUMBER || length > MAX_LETTER_NUMBER || !regex.test(word)) {
        return memo;
      }
      try {
        memo[ length - MIN_LETTER_NUMBER ].push(word);
      } catch (e) {
        console.error(e);
      }

      return memo;
    }, new Array(range).fill(0).map(() => []));

    await Promise.all(dicts.map(async (dict, index) => {
      let path = resolve(__dirname, `../public/more/${short}`);
      if (!existsSync(path)) {
        await mkdir(path, { recursive: true });
      }
      dict = shuffle(dict);
      path = `${path}/s${index + MIN_LETTER_NUMBER}.txt`;
      return writeFile(path, dict.join(','), 'utf8');
    }));
  }
})();
