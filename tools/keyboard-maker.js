const {
  readFile,
  writeFile,
} = require('fs/promises');
const { resolve } = require('path');

const path = resolve(__dirname, './keyboard-layouts.txt');

(async () => {
  const content = await readFile(path, 'utf8');
  const layouts = content.split('\n').reduce((acc, line) => {
    if (!line) {
      return acc;
    }
    const [name, keys] = line.split(':');
    if (!keys) {
      return acc;
    }

    acc[ name ] = keys.split('|');
    return acc;
  }, {});
  const json = JSON.stringify(layouts, null, 2);
  await writeFile(resolve(__dirname, '../src/data/keyboard-layouts.ts'), `const layouts:Record<string, string[]> = ${json};
export default layouts;`);
})();
