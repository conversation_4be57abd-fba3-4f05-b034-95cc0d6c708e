const { promisify } = require('util');
const { basename, extname, resolve } = require('path');
const exec = promisify(require('child_process').exec);

async function requireTs (path) {
  const tmp = resolve(__dirname, '../tmp');
  const command = `npx tsc ${path} -m commonjs --outDir ${tmp}`;
  await exec(command);
  const file = basename(path, extname(path));
  return require(`${tmp}/${file}`);
}

module.exports = {
  requireTs,
};
