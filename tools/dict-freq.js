const { readFile, writeFile } = require('fs/promises');
const { resolve } = require('path');
const { shuffle } = require('lodash');

const MIN_LETTER_NUMBER = 4;
const MAX_LETTER_NUMBER = 11;
const range = MAX_LETTER_NUMBER - MIN_LETTER_NUMBER + 1;

(async () => {
  let path = resolve(__dirname, './bad-words.txt');
  let content = await readFile(path, 'utf8');
  const badWords = content.split('\n').reduce((acc, word) => {
    const length = word.length;
    if (length < MIN_LETTER_NUMBER || length > MAX_LETTER_NUMBER) {
      return acc;
    }
    acc[ length - MIN_LETTER_NUMBER ].push(word);
    return acc;
  }, new Array(range).fill(0).map(() => []));
  path = resolve(__dirname, './unigram_freq.csv');
  content = await readFile(path, 'utf8');
  const result = content
    .split('\n')
    .slice(1)
    .reduce((memo, line) => {
      const [word] = line.split(',');
      const index = word.length - MIN_LETTER_NUMBER;
      if (index < 0 || index >= range) {
        return memo;
      }
      memo[ index ].push(word);
      return memo;
    }, new Array(range).fill(0).map(() => []));

  await Promise.all(result.map(async (words, index) => {
    let path = resolve(__dirname, `../public/f${index + MIN_LETTER_NUMBER}.txt`);
    const frequency = words.slice(0, 2500);
    const _badWords = badWords[ index ];
    for (let i = 0, len = frequency.length; i < len; i++) {
      if (_badWords.includes(frequency[ i ])) {
        const [word] = frequency.splice(i, 1);
        words.push(word);
        i--;
        len--;
      }
    }
    let missing = 2500 - frequency.length;
    let i = 2500;
    while (missing > 0) {
      const word = words[ i ];
      if (!_badWords.includes(word)) {
        frequency.push(word);
        words.splice(i, 1);
        missing--;
      } else {
        i++;
      }
    }
    await writeFile(path, shuffle(frequency).join(','), 'utf8');
    path = resolve(__dirname, `../public/s${index + MIN_LETTER_NUMBER}.txt`);
    await writeFile(path, words.slice(i).join(','), 'utf8');
  }));
  console.log('done');
})();
