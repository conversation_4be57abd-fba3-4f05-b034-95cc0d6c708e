<script lang="ts" setup>
const { t } = useI18n();
const route = useRoute();
const localePath = useLocalePath();

const { data, error } = await useAsyncData(
  'content-' + route.fullPath,
  async function () {
    return await queryCollection('content').path(route.path).first();
  },
)

if (error.value && import.meta.server) {
  setResponseStatus(404);
}

definePageMeta({
  layout: 'doc',
});
</script>

<template>
  <main class="prose md:prose-xl mx-auto py-8 px-4 sm:px-0">
    <content-renderer v-if="data" :value="data">
      <template #not-found>
        <h1>404</h1>
        <p>{{ t('Content not found') }}</p>
        <nuxt-link
          class="btn btn-primary text-base-100 btn-block"
          :to="localePath('/')"
        >
          {{ t('Back to Home') }}
        </nuxt-link>
      </template>
    </content-renderer>
  </main>
</template>

<script lang="ts">
export default {
  name: 'ContentPage',
}
</script>
