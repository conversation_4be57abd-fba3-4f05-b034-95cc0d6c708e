export type LanguageItem = {
  short:string;
  flag:string;
}
const Languages:Record<string, LanguageItem> = {
  'English(all)': {
    short: 'en',
    flag: '🌍',
  },
  'English(US)': {
    short: 'en-US',
    flag: '🇺🇸',
  },
  'English(UK)': {
    short: 'en-GB',
    flag: '🇬🇧'
  },
  'Russian': {
    short: 'ru',
    flag: '🇷🇺',
  },
  'Polish': {
    short: 'pl',
    flag: '🇵🇱',
  },
  'Portuguese': {
    short: 'pt',
    flag: '🇵🇹',
  },
  'French': {
    short: 'fr',
    flag: '🇫🇷',
  },
  'Italian': {
    short: 'it',
    flag: '🇮🇹',
  },
  'German': {
    short: 'de',
    flag: '🇩🇪',
  },
  'Spanish': {
    short: 'es',
    flag: '🇪🇸',
  },
  'Turkish': {
    short: 'tr',
    flag: '🇹🇷',
  },
  'Swedish': {
    short: 'sv',
    flag: '🇸🇪',
  },
  'Indonesian': {
    short: 'id',
    flag: '🇮🇩',
  },
  'Czech': {
    short: 'cs',
    flag: '🇨🇿',
  },
  'Greek': {
    short: 'el',
    flag: '🇬🇷',
  },
  'Dutch': {
    short: 'nl',
    flag: '🇳🇱',
  },
  'Norwegian': {
    short: 'no',
    flag: '🇳🇴',
  },
  'Finnish': {
    short: 'fi',
    flag: '🇫🇮',
  },
  'Ukrainian': {
    short: 'uk',
    flag: '🇺🇦',
  },
};
export default Languages;
