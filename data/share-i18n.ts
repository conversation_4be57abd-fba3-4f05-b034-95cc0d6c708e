import customNew from './share-i18n/share-custom-new.txt?raw';
import customPlaying from './share-i18n/share-custom-playing.txt?raw';
import customSuccess from './share-i18n/share-custom-success.txt?raw';
import customFail from './share-i18n/share-custom-fail.txt?raw';
import unlimitedNew from './share-i18n/share-unlimited-new.txt?raw';
import unlimitedPlaying from './share-i18n/share-unlimited-playing.txt?raw';
import unlimitedSuccess from './share-i18n/share-unlimited-success.txt?raw';
import unlimitedFail from './share-i18n/share-unlimited-fail.txt?raw';
import type { ShareTexts } from '@/types';

const shareTexts:ShareTexts = {
  custom: {
    newGame: customNew,
    playing: customPlaying,
    success: customSuccess,
    fail: customFail,
  },
  unlimited: {
    newGame: unlimitedNew,
    playing: unlimitedPlaying,
    success: unlimitedSuccess,
    fail: unlimitedFail,
  },
};

export default shareTexts;
