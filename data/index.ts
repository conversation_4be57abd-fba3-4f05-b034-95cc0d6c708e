import type { LetterNumbers } from '~/types';

export const MAX_STEP_BASE = 5;
export const MIN_LETTER_NUMBER = 4;
export const MAX_LETTER_NUMBER = 11;
export const LETTER_RANGE = MAX_LETTER_NUMBER - MIN_LETTER_NUMBER + 1;
export const SPECIFIED_WORD = 100;
export const LETTERS: LetterNumbers[] = new Array(LETTER_RANGE)
  .fill('')
  .map((_, i) => i + MIN_LETTER_NUMBER as LetterNumbers);

let _counter = 0;
export const counter = {
  get() {
    return _counter;
  },
  add() {
    _counter++;
  },
  next() {
    return _counter++;
  },
}
export const DEFAULT_LANGUAGE = 'English(all)';

export const Medals = ['🏅', '🥈', '🥉'];
