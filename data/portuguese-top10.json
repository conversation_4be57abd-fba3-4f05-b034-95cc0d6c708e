[{"category": "🔥 Trending", "topics": {"🔥 Trending": {"id": "__head_id", "words": ["teera", "amora", "italia", "tangerina", "vituperio", "peixe", "patronum", "irlanda", "bulgaria", "bolivia", "coccix", "polonia", "azerbaijao", "senegal", "bols<PERSON>ro", "pudim", "argentina", "pressao", "<PERSON><PERSON><PERSON><PERSON>", "lituania"]}, "🇪🇺 Países Europeus": {"id": 117, "words": ["russia", "ucrania", "franca", "espanha", "suecia", "<PERSON><PERSON><PERSON>", "aleman<PERSON>", "finlandia", "polonia", "italia", "romenia", "grecia", "bulgaria", "islandia", "hungria", "portugal", "austria", "servia", "irlanda", "lituania", "latvia", "croacia", "<PERSON>lovaq<PERSON><PERSON>", "estonia", "dinamarca", "suica", "<PERSON><PERSON><PERSON>", "moldavia", "belgica", "albania", "turquia", "eslovenia", "montenegro", "kosovo", "azerbaijao", "luxemburgo", "georgia", "andorra", "malta", "sanmarino", "monaco", "vaticano", "chipre", "armenia"]}, "🇺🇳 Países": {"id": 111, "words": ["uruguai", "peru", "bolivia", "chile", "china", "egito", "india", "venezuela", "japao", "coreiadosul", "equador", "cazaquistao", "argelia", "nigeria", "paraguai", "libia", "etiopia", "angola", "colombia", "canada", "antartida", "namibia", "brasil", "zambia", "israel", "quenia", "mexico", "camaroes", "marrocos", "iraque", "vietna", "jamaica", "malasia", "filipinas", "gabao", "catar", "senegal", "tunisia", "honduras", "tailandia", "guatemala", "argentina", "turquia", "panama", "srilanka", "africadosul", "costarica", "madagascar"]}, "🍉 Frutas": {"id": 115, "words": ["banana", "abacaxi", "abacate", "<PERSON><PERSON><PERSON>", "melancia", "carambola", "damasco", "fram<PERSON><PERSON>", "goiaba", "graviola", "jabuticaba", "pessego", "<PERSON><PERSON><PERSON><PERSON>", "tamarindo", "tangerina", "acerola", "amora", "cacau", "caqui", "tomate", "cereja", "g<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "limao", "<PERSON><PERSON><PERSON><PERSON>"]}, "🍜 Comida": {"id": 116, "words": ["batatafrita", "r<PERSON><PERSON><PERSON>", "sorvete", "biscoito", "pizza", "bolacha", "torta", "hamburger", "picole", "queijo", "churros", "macarrao", "sopa", "milkshake", "chocolate", "carne", "peixe", "frango", "omelete", "sand<PERSON>che", "sushi", "pudim", "pipoca", "leite", "farofa", "quindim", "tapioca"]}, "🏙️ Cidades Brasileiras": {"id": 113, "words": ["cabofrio", "niteroi", "saopaulo", "goiania", "salvador", "fortaleza", "curitiba", "manaus", "recife", "brasilia", "belem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "campinas", "sa<PERSON><PERSON>s", "maceio", "natal", "teresina", "joa<PERSON><PERSON><PERSON>", "osasco", "uberlandia", "contagem", "sorocaba", "<PERSON><PERSON><PERSON><PERSON>", "macae", "joinville", "juizdefora", "londrina", "macapa", "santos", "betim", "diadema", "jund<PERSON>i", "o<PERSON>a", "bauru", "vitoria", "carua<PERSON>", "blumena<PERSON>", "petropolis", "<PERSON><PERSON><PERSON>", "itaborai", "juazeiro", "criciuma", "chapeco", "marica"]}, "🏠 Cidades Internacionais": {"id": 114, "words": ["xangai", "lagos", "istambul", "moscou", "pequim", "toquio", "cairo", "novaiorque", "londres", "bogota", "hongkong", "bagda", "<PERSON><PERSON><PERSON><PERSON>", "chicago", "osaka", "washington", "boston", "buenosa<PERSON>", "dallas", "lasvegas", "detroit", "paris", "filadelfia", "baltimore", "atlanta", "miami", "toronto", "seatlle", "madri", "barcelona", "singapura", "sydney", "pequim", "mon<PERSON><PERSON>", "cairo", "baltimore", "milao", "teera", "viena", "telaviv", "portland", "calcuta", "monter<PERSON>", "lisboa", "atenas", "vancouver", "porto", "berlin", "jacarta", "manchester"]}, "🔥 Top 10 Semanal de Palavas Personalizadas": {"id": 97, "words": ["camara", "bolivia", "lucas", "bruno", "<PERSON><PERSON><PERSON><PERSON>", "daniel", "silveira", "<PERSON><PERSON><PERSON><PERSON>", "argentina", "<PERSON><PERSON><PERSON><PERSON>"]}, "🔬 Ciência": {"id": 99, "words": ["celula", "atomo", "massa", "pressao", "teoria", "frasco", "provas", "fossil", "metodo", "formula"]}, "😭 Palavras Difíceis": {"id": 98, "words": ["adstrito", "<PERSON><PERSON><PERSON>", "ef<PERSON><PERSON>", "frugal", "<PERSON><PERSON><PERSON>", "ignobil", "coccix", "loquaz", "oprobrio", "vituperio"]}, "🤴🏽 Presidentes do Brasil": {"id": 112, "words": ["bols<PERSON>ro", "michel", "temer", "dilma", "r<PERSON><PERSON><PERSON>", "lula", "fernando", "<PERSON><PERSON><PERSON><PERSON>", "cardoso", "<PERSON><PERSON><PERSON>", "franco", "collor", "mello", "sarney", "tancredo", "neves", "bap<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ernesto", "geisel", "<PERSON><PERSON><PERSON><PERSON>", "medici", "rademaker", "<PERSON><PERSON><PERSON>", "tavares", "<PERSON>hur", "costa", "silva", "castello", "branco", "<PERSON><PERSON><PERSON>", "janio", "quadros", "jucelino", "kubitschek", "<PERSON>os", "filho", "<PERSON><PERSON><PERSON>", "vargas", "eurico", "gaspar", "<PERSON><PERSON>", "linhares", "fragoso"]}, "🧙‍♀️ Palavras Mágicas de Harry Potter": {"id": 110, "words": ["avada", "portus", "accio", "lumos", "imperius", "crucio", "<PERSON><PERSON><PERSON>", "expecto", "patronum", "cruciatus"]}}}]