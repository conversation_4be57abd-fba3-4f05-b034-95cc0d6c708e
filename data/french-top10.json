[{"category": "🔥 Trending", "topics": {"🔥 Trending": {"id": "__head_id", "words": ["Rouen", "Caen", "<PERSON><PERSON><PERSON>", "homme", "Maroc", "Niger", "harry", "Comores", "pomme", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "tartiflette", "<PERSON><PERSON><PERSON>", "syrie", "japon", "FOLIE", "Rwanda", "<PERSON><PERSON><PERSON><PERSON>", "tissu", "egypte"]}, "🇫🇷 French speaking countries": {"id": 93, "words": ["Bélgique", "Canada", "Luxembourg", "Sénégal", "Togo", "Monaco", "Gabon", "Burkinafaso", "<PERSON><PERSON><PERSON>", "Maroc", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Congo", "Mali", "Tchad", "Suisse", "Comores", "Seychelles", "Guinée", "<PERSON><PERSON><PERSON>", "Rwanda", "Madagascar", "Niger", "Burundi", "Djibouti", "Vanuatu", "France"]}, "🇺🇳 Countries:": {"id": 91, "words": ["fiji", "<PERSON><PERSON><PERSON>", "guam", "chili", "chine", "egypte", "inde", "kenya", "japon", "<PERSON><PERSON><PERSON>", "malte", "espagne", "italie", "syrie", "soudan", "Haiti", "népal", "lettonie", "france", "Canada", "norvège", "pologne", "br<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "serbie", "chypre", "mexique", "finlande", "denemark", "i<PERSON><PERSON>", "allemagne", "jam<PERSON><PERSON><PERSON>", "estonie", "<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON><PERSON>", "i<PERSON><PERSON>", "hong<PERSON>", "autriche", "Portugal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indonésie", "argentine", "Singapour", "australie", "luxembourg", "paysbas", "suisse"]}, "🍉 Fruits": {"id": 95, "words": ["banane", "orange", "clémentine", "pêche", "poire", "raisin", "kiwi", "fraise", "pomme", "melon", "cerise", "mandarine", "dates", "ananas", "mangue", "a<PERSON><PERSON>", "framboise", "pastèque", "citron", "cassis", "goyave", "fig", "litchi", "avocat", "kaki"]}, "🍜 Foods": {"id": 96, "words": ["couscous", "steakfrites", "raclette", "cassoulet", "choucroute", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pizza", "lasagnes", "tartiflette", "hu<PERSON><PERSON>", "poutine", "kebab", "fondue", "baguette", "<PERSON><PERSON><PERSON>", "éclair", "gratin", "sushi", "tacos", "croissant", "brie", "frites", "nouilles", "galette"]}, "🏠 French major cities": {"id": 94, "words": ["Paris", "Marseille", "Lyon", "Toulouse", "Nice", "Nantes", "Strasbourg", "Montpellier", "Bordeaux", "Lille", "<PERSON><PERSON>", "Reims", "LeHavre", "Toulon", "Grenoble", "<PERSON>s", "Dijon", "Brest", "<PERSON><PERSON>", "Amiens", "Metz", "Lille", "Orléans", "Caen", "R<PERSON><PERSON><PERSON>", "Versailles", "Cannes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Avignon", "Rouen", "Argenteuil", "Nancy", "Nanterre", "<PERSON><PERSON>", "Sarcelles", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Drancy", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, "🔥 Weekly Top 10 custom wordles:": {"id": 87, "words": ["femme", "homme", "Paris", "Kebab", "Salut", "<PERSON>our", "<PERSON><PERSON>", "Aider", "<PERSON><PERSON><PERSON>", "douce"]}, "🔬 Science:": {"id": 89, "words": ["cellule", "atome", "masse", "donn<PERSON>", "peser", "flacon", "phase", "fossile", "tissu", "science"]}, "😭 Hardest 10 wordles so far:": {"id": 88, "words": ["piste", "saper", "FOLIE", "DIEUX", "VALVE", "SAVON", "PACTE", "FAUTE", "DOYEN", "BEAUF"]}, "🤴🏽 French Celebrities": {"id": 92, "words": ["<PERSON><PERSON>", "Simoneveil", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Celinedion", "Yan<PERSON>noah", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Cocochanel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Paulpogba", "<PERSON><PERSON><PERSON>"]}, "🧙‍♀️ Harry Potter:": {"id": 90, "words": ["Baguette", "harry", "<PERSON><PERSON><PERSON>", "potter", "Attrapeur", "<PERSON><PERSON><PERSON>", "Sortilège", "Gallion", "<PERSON><PERSON><PERSON>", "Quidditch"]}}}]