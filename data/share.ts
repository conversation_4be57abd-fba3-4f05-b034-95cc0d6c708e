import customNew from './share-custom-new.txt?raw';
import customPlaying from './share-custom-playing.txt?raw';
import customSuccess from './share-custom-success.txt?raw';
import customFail from './share-custom-fail.txt?raw';
import unlimitedNew from './share-unlimited-new.txt?raw';
import unlimitedPlaying from './share-unlimited-playing.txt?raw';
import unlimitedSuccess from './share-unlimited-success.txt?raw';
import unlimitedFail from './share-unlimited-fail.txt?raw';
import type { ShareTexts } from '~/types';

const shareTexts:ShareTexts = {
  custom: {
    newGame: customNew,
    playing: customPlaying,
    success: customSuccess,
    fail: customFail,
  },
  unlimited: {
    newGame: unlimitedNew,
    playing: unlimitedPlaying,
    success: unlimitedSuccess,
    fail: unlimitedFail,
  },
};

export default shareTexts;
