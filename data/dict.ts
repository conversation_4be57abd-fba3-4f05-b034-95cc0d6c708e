import { MAX_LETTER_NUMBER, MIN_LETTER_NUMBER } from '@/data';
import languages from '@/data/languages';

const range = MAX_LETTER_NUMBER - MIN_LETTER_NUMBER + 1;
const dictionaries: (string[] | null)[] = Array(range * 2).fill(null);
const frequency: (string[] | null)[] = Array(range).fill(null);
export const DICT_VERSION = 4;

async function loadFrequencyEnglish(letter: number) {
  const response = await fetch(`/f${letter}.txt?v=${DICT_VERSION}`);
  const text = await response.text();
  const words = text.split(',');
  frequency[ letter - MIN_LETTER_NUMBER ] = words;
  dictionaries[ letter - MIN_LETTER_NUMBER ] = dictionaries[ letter - MIN_LETTER_NUMBER + range ] = words;
}

export async function loadDict(letter:number, lang = '') {
  const index:number = letter - MIN_LETTER_NUMBER;
  const full = dictionaries[ index ];
  const notEnglish = lang && !/^en/i.test(lang);
  if (notEnglish) {
    lang = languages[ lang ].short;
  } else if (!frequency[ letter ]) {
    await loadFrequencyEnglish(letter);
  }
  const _frequency = dictionaries[ index + range ];
  if (!full || (full === _frequency && !notEnglish)) {
    const response = await fetch(`${notEnglish ? '/more/' + lang : ''}/s${letter}.txt?v=${DICT_VERSION}`);
    const str = await response.text();
    const dict = str.replace(/\n$/mg, '').split(',');
    dictionaries[ index ] = notEnglish ? dict : [..._frequency!, ...dict];
    if (notEnglish) {
      dictionaries[ index + range ] = dict;
    }
  }
}

export function resetDict(langName:string) {
  const newDict = !langName || /^en/i.test(langName) ? [...frequency, ...frequency] : [];
  dictionaries.splice(0, dictionaries.length, ...newDict);
}

export default dictionaries;
