const layouts:Record<string, string[]> = {
  'el': [
    'ςερτυθιοπ',
    'ασδφγηξκλ',
    'ζχψωβνμ'
  ],
  'en': [
    'qwertyuiop',
    'asdfghjkl',
    'zxcvbnm'
  ],
  'pt': [
    'qwertyuiop',
    'asdfghjklç',
    'zxcvbnm'
  ],
  'es': [
    'qwertyuiop',
    'asdfghjklñ',
    'zxcvbnm'
  ],
  'cs': [
    'qwertyuiop',
    'asdfghjkl',
    'zxcvbnm',
    'áčěéíšřůýž'
  ],
  'fr': [
    'azertyuiop',
    'qsdfghjklmù',
    'wxcvbn'
  ],
  'de': [
    'qwertzuiopü',
    'asdfghjklöä',
    'yxcvbnm'
  ],
  'sv': [
    'qwertyuiopå',
    'asdfghjklöä',
    'zxcvbnm'
  ],
  'no': [
    'qwertyuiop<PERSON>',
    'asd<PERSON><PERSON><PERSON><PERSON>ø<PERSON>',
    'zxcvbnm'
  ],
  'fi': [
    'qwertyuiopå',
    'asdfghjklöä',
    'zxcvbnm'
  ],
  'it': [
    'qwertyuiopè',
    'asdfghjklòàù',
    'zxcvbnm'
  ],
  'tr': [
    'qwertyuıopğü',
    'asdfghjklşi̇',
    'zxcvbnmöç'
  ],
  'ru': [
    'йцукенгшщзхъ',
    'фывапролджэ',
    'ячсмитьбю'
  ],
  'pl': [
    'qwertzuiopżś',
    'asdfghjklłąó',
    'yxcvbnm'
  ],
  'uk': [
    'йцукенгшщзхї',
    'фівапролджє',
    'ячсмитьбюґ'
  ]
};
export default layouts;