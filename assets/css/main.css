@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --pure-bg: #fff;
  --text-color: #445857;
  --light-bg: #dfeaea;
  --border-color: #5a706e;
  --light-border-color: #d7e4e4;
  --hr-color: #c5d7d6;
  --win-bg: #87de9e;
  --ad-banner-height: 0px;
  --header-button-bg: #5f9997;
  --header-button-border: #74bdbb;
  --color-bg: #7aadac;
  --footer-active-bg: #468381;
  --grid-muted-bg: #cfcfcf;
  --grid-muted-color: #b1b1b1;
  --grid-maybe-bg: #ffbb38;
  --key-bg: #bfd6d5;
  --key-color: #5a6f6f;
  --key-shadow: #9dc1bf;
  --key-muted-color: #fff;
  --key-muted-bg: #ebebeb;
  --key-win-bg: #86de9e;
  --key-win-shadow: #59c578;
  --key-maybe-bg: #ffbb38;
  --key-maybe-shadow: #f1a93d;
  --modal-border-color: #90bab9;
  --share-button-bg: #2baf48;
  --share-input-bg: #b0cecd;
  --switch-bg: #7f8e8e;
  --underline-color: #4c70a2;
  --underline-bg: #dfeaea;
}
.dark {
  --pure-bg: #28274f;
  --color-bg: #1a1a2b;
  --light-bg: #1a1a2b;
  --border-color: #5754a3;
  --text-color: #fff;
  --hr-color: #4a4881;
  --win-bg: #22b089;
  --header-button-bg: #1a1a2b;
  --header-button-border: #403e76;
  --footer-active-bg: #343362;
  --grid-dark-bg: #07070a;
  --grid-muted-bg: #414067;
  --grid-maybe-bg: #d76143;
  --key-bg: #353362;
  --key-color: #fff;
  --key-shadow: #9dc1bf;
  --key-muted-color: #363566;
  --key-muted-bg: #242346;
  --key-win-bg: #24b089;
  --key-win-shadow: #59c578;
  --key-maybe-bg: #d76143;
  --count-down-color: #d86144;
  --switch-bg: #1a1a2b;
  --underline-color: #a4ccff;
  --underline-bg: #343362;
}
.high-contrast {
  --win-bg: #f5793a;
  --key-win-bg: #f5793a;
  --key-win-shadow: #d86930;
  --grid-maybe-bg: #86c0f9;
  --key-maybe-bg: #86c0f9;
  --key-maybe-shadow: #659dd5;
}
@media (max-width: 440px) {
  :root {
    --ad-banner-height: 100px;
  }
}
@media (min-width: 441px) {
  :root {
    --ad-banner-height: 250px;
  }
  #banner-ad {
    background-color: transparent;
  }
}
#ezoic-pub-ad-placeholder-101 {
  width: 100%;
  max-width: 970px;
  margin: 0 auto;
}
@media (max-width: 440px) {
  #ezoic-pub-ad-placeholder-101 {
    height: var(--ad-banner-height);
  }
}
@media (min-width: 441px) {
  #ezoic-pub-ad-placeholder-101 {
    min-height: var(--ad-banner-height);
  }
}
#btm-darkmode {
  opacity: 0 !important;
}
#btm-share {
  padding-right: 0 !important;
}
.btm-tooltip svg {
  display: inline;
}
@media (max-width: 1280px) {
  #ez-sidebar-wall-left {
    display: none !important;
  }
}
@media (min-width: 1281px) {
  #ez-sidebar-wall-left {
    left: 80px !important;
  }
}
.grid.sketch > div {
  aspect-ratio: 1;
  border-radius: 0.5rem;
  animation-duration: var(--sketch-duration);
  animation-fill-mode: both;
  animation-iteration-count: infinite;
  animation-name: sketch;
  animation-timing-function: ease-in-out;
}
.grid.sketch > div:nth-child(n + 5) {
  animation-delay: 0.25s;
}
.grid.sketch > div:nth-child(n + 10) {
  animation-delay: 0.5s;
}
.grid.sketch > div:nth-child(n + 15) {
  animation-delay: 0.75s;
}
.grid.sketch > div:nth-child(n + 20) {
  animation-delay: 1s;
}
.grid.sketch > div:nth-child(n + 25) {
  animation-delay: 1.25s;
}
.game-item.flex,
.face.back,
.grid.sketch > div {
  background-color: var(--light-bg);
  border-color: var(--border-color);
}
.keyboard-placeholder {
  height: 35.6vw;
  max-height: 166px;
  padding-bottom: 1rem;
}
.theme-item {
  break-inside: avoid;
  background-color: var(--pure-bg);
  border-color: var(--header-button-border);
  color: var(--key-color);
  content-visibility: auto;
}
.theme-item header {
  border-color: var(--header-button-border);
}
@-moz-keyframes sketch {
  from {
    transform: scale3d(1, 1, 1);
  }
  25% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  50% {
    transform: scale3d(1, 1, 1);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
@-webkit-keyframes sketch {
  from {
    transform: scale3d(1, 1, 1);
  }
  25% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  50% {
    transform: scale3d(1, 1, 1);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
@-o-keyframes sketch {
  from {
    transform: scale3d(1, 1, 1);
  }
  25% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  50% {
    transform: scale3d(1, 1, 1);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
@keyframes sketch {
  from {
    transform: scale3d(1, 1, 1);
  }
  25% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  50% {
    transform: scale3d(1, 1, 1);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
.bi {
  background-color: currentColor;
  display: inline-block;
  mask-size: 1em 1em;
  mask-repeat: no-repeat;
  mask-position: center;
}
.bi::before {
  display: inline-block;
  content: '';
  width: 1em;
}
.bi-twitter {
  mask-image: url("../../node_modules/bootstrap-icons/icons/twitter.svg");
}
.bi-facebook {
  mask-image: url("../../node_modules/bootstrap-icons/icons/facebook.svg");
}
body {
  background-color: var(--pure-bg);
  margin: 0;
  -webkit-font-smoothing: antialiased;
}
:root {
  --app-max-width: 27.5rem;
  --sketch-duration: 3s;
}
@media (max-width: 575px) {
  :root {
    --app-max-width: 100%;
  }
}
#app {
  width: 100%;
  min-width: 20rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}
.aw-container {
  max-width: var(--app-max-width);
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}
.color-bg {
  background-color: var(--color-bg);
  color: #fff;
  content-visibility: auto;
  contain-intrinsic-size: 1px 2000px;
}
.disabled,
[disabled] {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}
.bg-twitter {
  background-color: #1d9bf0;
}
.bg-facebook {
  background-color: #3b5998;
}
.text-xs2 {
  font-size: 0.625rem;
  line-height: 0.75rem;
}
.text-key-color {
  color: var(--key-color);
}
#header {
  background-color: var(--color-bg);
  color: #fff;
  .inner {
    padding: 0 0.5rem;
    max-width: var(--app-max-width);
    margin: 0 auto;
    button {
      background-color: var(--header-button-bg);
      border: 1px solid var(--header-button-border);
      border-radius: 0.25rem;
      margin-top: 0.375rem;
      margin-bottom: 0.375rem;
      margin-left: 0.25rem;
      width: 2.25rem;
      height: 2.25rem;
      outline: none;
    }
    button:focus {
      outline: none;
      box-shadow: none;
    }
    button:first-child {
      margin-left: 0;
    }
    button i {
      color: #fff;
      display: block;
      width: 1.75rem;
      height: 1.75rem;
      margin: 0 auto;
    }
    .language-button {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 0.75rem;
      width: 3.5em;
    }
    .language-button span:not(:last-child) {
      display: block;
    }
  }
}
.dark .game-item.active {
  background-color: var(--grid-dark-bg);
}
.dark .keyboard {
  background-color: rgba(40,39,79,0.5);
}
.dark .keyboard-item,
.dark .keyboard-item.win,
.dark .keyboard-item.maybe {
  box-shadow: inset 0 -0.1875rem rgba(0,0,0,0.4);
}
.dark .keyboard-item:active,
.dark .keyboard-item.win:active,
.dark .keyboard-item.maybe:active {
  box-shadow: inset 0 0.1875rem var(--pure-bg);
}
.dark .modal-box {
  background-color: var(--pure-bg);
}
.dark .stat-number {
  color: var(--key-win-bg);
}
.dark .w-m-4 progress::-webkit-progress-bar {
  background-color: var(--grid-muted-bg);
}
.pressable-button {
  box-shadow: inset 0 -0.1875rem rgba(0,0,0,0.4);
  line-height: calc(var(--line-height) - 0.1875rem);
  padding-bottom: 0.1875rem;
  transition: all 0.2s ease-in-out;
}
.pressable-button:hover {
  filter: brightness(110%);
}
.pressable-button:active {
  box-shadow: inset 0 0.1875rem var(--pure-bg);
  padding-top: 0.1875rem;
  padding-bottom: 0;
}
.bg-yellow-500 .pressable-button {
  background-color: var(--key-bg);
}
body > .notification {
  top: calc(var(--ad-banner-height) + 10rem);
}
.pwa-toast {
  z-index: 200000;
}
@media (max-width: 420px) and (min-width: 381px) {
  #languages {
    font-size: 0.875rem;
  }
}
@media (max-width: 380px) {
  #languages {
    font-size: 0.75rem;
  }
}
#themes-list {
  font-size: 0.875rem;
}
footer .active {
  background-color: var(--footer-active-bg);
}
#docs-footer a {
  color: #415599;
  font-weight: bold;
}
.dark #docs-footer a {
  color: #fff;
}
.switch-wrapper {
  background-color: var(--switch-bg);
  border-radius: 1rem;
  cursor: pointer;
  display: block;
  width: 3rem;
  height: 1.5rem;
  position: relative;
  transition: background-color 0.2s;
}
.switch-wrapper::before {
  background-color: #fff;
  content: '';
  border-radius: 0.625rem;
  width: 1.25rem;
  height: 1.25rem;
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  transition: transform 0.2s;
}
:checked + .switch-wrapper {
  background-color: #1ba1ff;
}
:checked + .switch-wrapper::before {
  transform: translateX(1.5rem);
}
.dark {
  :checked + .switch-wrapper {
    background-color: #548d4e;
  }
  .high-contrast :checked + .switch-wrapper {
    background-color: #f5793a;
  }
}
.Unlimited {
  background-color: #415599;
  color: #fff;
}
.custom {
  background-color: #b5a749;
}
@media (max-width: 374px) {
  #unlimited-tag {
    position: absolute;
    top: 2.5em;
  }
}
.modal-box {
  color: var(--text-color);
}
.modal-box hr {
  border-color: var(--hr-color);
}
.modal-box .game-item.win {
  background-color: var(--win-bg);
}
.modal-box .game-item.muted {
  background-color: var(--grid-muted-bg);
  color: var(--grid-muted-color);
}
.modal-box .game-item.maybe {
  background-color: var(--grid-maybe-bg);
}
.modal-box .pressable-button {
  --line-height: 2.5rem;
}
.modal-box .border-b,
.modal-box .border-t {
  border-color: var(--hr-color);
}
.w-m-4 {
  width: calc(100% - 1rem);
}
.w-m-4 progress {
  display: block;
  width: 100%;
  border-radius: 0.5rem;
  margin: 3px 0;
}
.w-m-4 progress::-webkit-progress-bar {
  background-color: var(--light-bg);
  border-radius: 0.5rem;
}
.w-m-4 progress::-webkit-progress-value {
  background-color: var(--win-bg);
  border-radius: 0.5rem;
}
.w-m-4 progress::-moz-progress-bar {
  background-color: var(--win-bg);
  border-radius: 0.5rem;
}
.stat-items {
  background-color: var(--light-bg);
  border: 1px solid var(--modal-border-color);
  border-radius: 0.5rem;
}
.stat-item {
  flex: 1 0 25%;
  padding: 0.5rem 0;
}
.stat-item:not(:first-child) {
  border-left: 1px solid var(--modal-border-color);
}
.stat-number {
  line-height: 1.875rem;
}
.share-button {
  background-color: var(--share-button-bg);
}
.count-down {
  color: var(--count-down-color);
}
.share-input {
  background-color: var(--light-bg);
  box-shadow: inset 0 0.125rem 0.125rem 1px rgba(0,0,0,0.07);
  font-size: 1.25rem;
  font-weight: bold;
  letter-spacing: 0.25rem;
}
.share-input::placeholder {
  color: var(--text-color);
  font-size: 0.75rem;
  font-weight: normal;
  letter-spacing: 0;
  position: relative;
  top: -0.125rem;
}
.letter-item {
  background-color: var(--light-bg);
  color: var(--text-color);
}
.letter-item.active {
  background-color: var(--win-bg);
}
.underline-button {
  background-color: var(--underline-bg);
  border-radius: 1.125rem;
  color: var(--underline-color);
  font-size: 0.75rem;
  line-height: 2.25;
  padding: 0 1.25em;
  text-decoration: underline;
  white-space: nowrap;
}
.underline-button:hover {
  text-decoration: none;
}
.game-item {
  aspect-ratio: 1;
  color: var(--text-color);
  position: relative;
  text-transform: uppercase;
  transform-style: preserve-3d;
  user-select: none;
}
.face {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.face:first-child {
  transform: translateZ(0.1px);
}
.face:last-child {
  transform: rotateX(180deg);
}
.face.muted {
  background-color: var(--grid-muted-bg);
  color: var(--grid-muted-color);
}
.face.win {
  background-color: var(--win-bg);
}
.face.maybe {
  background-color: var(--grid-maybe-bg);
}
.flip {
  animation-name: flip;
}
@-moz-keyframes flip {
  from {
    transform: perspective(400px) rotate3d(1, 0, 0, 0);
  }
  50% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
  }
  to {
    transform: perspective(400px) rotate3d(1, 0, 0, 180deg);
  }
}
@-webkit-keyframes flip {
  from {
    transform: perspective(400px) rotate3d(1, 0, 0, 0);
  }
  50% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
  }
  to {
    transform: perspective(400px) rotate3d(1, 0, 0, 180deg);
  }
}
@-o-keyframes flip {
  from {
    transform: perspective(400px) rotate3d(1, 0, 0, 0);
  }
  50% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
  }
  to {
    transform: perspective(400px) rotate3d(1, 0, 0, 180deg);
  }
}
@keyframes flip {
  from {
    transform: perspective(400px) rotate3d(1, 0, 0, 0);
  }
  50% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
  }
  to {
    transform: perspective(400px) rotate3d(1, 0, 0, 180deg);
  }
}
.keyboard {
  background-color: rgba(255,255,255,0.75);
  touch-action: manipulation;
  position: sticky;
  left: 0;
  width: 100%;
  bottom: 0;
}
.keyboard svg {
  pointer-events: none;
}
@media (min-width: 441px) {
  .keyboard {
    margin-bottom: 4rem;
  }
}
@media (max-width: 440px) {
  .keyboard {
    margin-bottom: 1rem;
  }
  .smm-ready .keyboard {
    bottom: 3rem;
  }
}
.letter-9 .keyboard-item,
.letter-10 .keyboard-item {
  aspect-ratio: 0.8125;
  width: 9.1%;
}
.letter-9 .keyboard-item + .keyboard-item,
.letter-10 .keyboard-item + .keyboard-item {
  margin-left: 1%;
}
.letter-9 .keyboard-item:nth-child(n + 11),
.letter-10 .keyboard-item:nth-child(n + 11) {
  margin-top: 1%;
}
.letter-9 .keyboard-item:nth-child(11),
.letter-10 .keyboard-item:nth-child(11) {
  margin-left: 0;
}
.greek .keyboard-item:nth-child(1),
.greek .keyboard-item:nth-child(10) {
  margin-left: 4.55%;
}
.greek .keyboard-item:nth-child(9),
.greek .keyboard-item:nth-child(18) {
  margin-right: 4.55%;
}
.greek .keyboard-item:nth-child(10) {
  margin-top: 1%;
}
.greek .keyboard-item:nth-child(11) {
  margin-left: 1%;
}
.letter-11 .keyboard-item {
  aspect-ratio: 0.75;
  width: 8.4%;
}
.letter-11 .keyboard-item + .keyboard-item {
  margin-left: 0.76%;
}
.letter-11 .keyboard-item:nth-child(n + 12) {
  margin-top: 1%;
}
.letter-11 .keyboard-item:nth-child(12) {
  margin-left: 0;
}
.french .keyboard-item:nth-child(1) {
  margin-left: 4.2%;
}
.french .keyboard-item:nth-child(10) {
  margin-right: 4.2%;
}
.french .keyboard-item:nth-child(11) {
  margin-top: 1%;
  margin-left: 0;
}
.french .keyboard-item:nth-child(12) {
  margin-left: 0.76%;
}
.letter-12 .keyboard-item {
  aspect-ratio: 0.68;
  width: 7.6%;
}
.letter-12 .keyboard-item + .keyboard-item {
  margin-left: 0.8%;
}
.letter-12 .keyboard-item:nth-child(n + 13) {
  margin-top: 1%;
}
.letter-12 .keyboard-item:nth-child(13) {
  margin-left: 0;
}
.italian .keyboard-item:nth-child(1) {
  margin-left: 3.8%;
}
.italian .keyboard-item:nth-child(11) {
  margin-right: 3.8%;
}
.italian .keyboard-item:nth-child(12) {
  margin-top: 1%;
  margin-left: 0;
}
.italian .keyboard-item:nth-child(13) {
  margin-left: 0.76%;
}
.turkish .keyboard-item-enter,
.russian .keyboard-item-enter,
.turkish .keyboard-item-delete,
.russian .keyboard-item-delete {
  flex-basis: 11%;
  font-size: 0.875em;
  overflow: hidden;
}
.ukrainian .keyboard-item:nth-child(13) {
  margin-left: 4.2%;
}
.ukrainian .keyboard-item:nth-child(23) {
  margin-right: 4.2%;
}
.ukrainian .keyboard-item-enter,
.ukrainian .keyboard-item-delete {
  flex-basis: 7.5%;
  font-size: 0.75em;
  overflow: hidden;
}
.czech .keyboard-item:nth-child(27) {
  margin-left: 0;
}
.keyboard-item {
  background-color: var(--key-bg);
  border-radius: 0.375rem;
  color: var(--key-color);
  cursor: pointer;
  padding-bottom: 0.1875rem;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  text-transform: capitalize;
  user-select: none;
  box-shadow: inset 0 -0.1875rem var(--key-shadow);
  transition: 0.2s ease-in-out;
}
.keyboard-item.muted {
  background-color: var(--key-muted-bg);
  color: var(--key-muted-color);
  box-shadow: none;
}
.keyboard-item.win {
  background-color: var(--key-win-bg);
  box-shadow: inset 0 -0.1875rem var(--key-win-shadow);
}
.keyboard-item.maybe {
  background-color: var(--key-maybe-bg);
  box-shadow: inset 0 -0.1875rem var(--key-maybe-shadow);
}
.keyboard-item:active,
.keyboard-item.active {
  box-shadow: inset 0 0.1875rem var(--pure-bg);
  padding-top: 0.1875rem;
  padding-bottom: 0;
}
.keyboard-item:hover {
  filter: brightness(110%);
}
.keyboard-item-enter {
  margin-left: 0 !important;
}
.keyboard-item-enter,
.keyboard-item-delete {
  aspect-ratio: unset !important;
  flex: 1 0 14%;
}
.fixed-topic header {
  border-color: var(--header-button-border);
}
.dark .fixed-topic {
  color: #fff;
}
@media (max-width: 640px) {
  .fixed-topic {
    top: 1.5rem;
    left: 5.5rem;
    right: 5.5rem;
    z-index: 1;
  }
  .fixed-topic h3 {
    padding-left: 0.5rem;
  }
  .fixed-topic h3 :first-child {
    max-width: calc(100vw - 16rem);
  }
  .fixed-topic .topic-item-inner {
    display: none;
  }
  .fixed-topic.open .topic-item-inner {
    display: block;
  }
}
@media (min-width: 641px) {
  .fixed-topic {
    margin-left: calc(var(--app-max-width) / 2 + 1.25rem);
    top: 5rem;
    left: 50%;
    width: 14rem;
  }
  .fixed-topic h3 {
    padding-left: 1rem;
  }
}
.success-word {
  color: var(--key-win-shadow);
  font-weight: bold;
}
@media (max-width: 640px) {
  .aw-tab {
    max-width: 100%;
  }
}
