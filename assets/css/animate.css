@import "animate.css/source/_vars.css";
@import "animate.css/source/_base.css";
@import "animate.css/source/fading_entrances/fadeIn.css";
@import "animate.css/source/fading_exits/fadeOut.css";
@import "animate.css/source/sliding_entrances/slideInUp.css";
@import "animate.css/source/sliding_exits/slideOutDown.css";
@import "animate.css/source/attention_seekers/shakeX.css";
@import "animate.css/source/attention_seekers/flash.css";
:root {
  --half-second: 0.5s;
}
.delay-1 {
  animation-delay: 0.125s;
}
.delay-2 {
  animation-delay: 0.25s;
}
.delay-3 {
  animation-delay: 0.375s;
}
.delay-4 {
  animation-delay: 0.5s;
}
.delay-5 {
  animation-delay: 0.625s;
}
.delay-6 {
  animation-delay: 0.75s;
}
.delay-7 {
  animation-delay: 0.875s;
}
.delay-8 {
  animation-delay: 1s;
}
.delay-9 {
  animation-delay: 1.125s;
}
.delay-10 {
  animation-delay: 1.25s;
}
.animated.faster2 {
  animation-duration: calc(var(--half-second) / 5);
}
.pulse {
  animation-name: pulse;
  animation-timing-function: ease-in-out;
}
@-moz-keyframes pulse {
  from {
    transform: scale3d(1, 1, 1);
  }
  50% {
    transform: scale3d(1.25, 1.25, 1.25);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
@-webkit-keyframes pulse {
  from {
    transform: scale3d(1, 1, 1);
  }
  50% {
    transform: scale3d(1.25, 1.25, 1.25);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
@-o-keyframes pulse {
  from {
    transform: scale3d(1, 1, 1);
  }
  50% {
    transform: scale3d(1.25, 1.25, 1.25);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
@keyframes pulse {
  from {
    transform: scale3d(1, 1, 1);
  }
  50% {
    transform: scale3d(1.25, 1.25, 1.25);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
