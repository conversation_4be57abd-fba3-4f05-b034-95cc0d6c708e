import pkg from './package.json' with { type: 'json' };
import i18n from './i18n/content.json' with { type: 'json' };
import Languages from './data/languages';
import { name2slug } from './utils';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  app: {
    head: {
      title: '',
    },
  },
  compatibilityDate: '2024-12-25',
  css: [
    'assets/css/main.css',
    'assets/css/animate.css',
  ],
  modules: [
    '@nuxt/eslint',
    '@nuxt/content',
    [
      '@pinia/nuxt',
      {
        autoLoad: [
          'defineStore',
        ],
      },
    ],
    '@nuxtjs/tailwindcss',
    '@nuxtjs/i18n',
    '@nuxtjs/sitemap',
    '@nuxtjs/seo'
  ],
  routeRules: {
    '/': { prerender: true },
    '/**': { isr: true },
  },
  runtimeConfig: {
    public: {
      hasAd: '',
      name: i18n.h1,
      siteUrl: '',
      googleGaId: '',
    },
  },
  i18n: {
    baseUrl: process.env.NUXT_PUBLIC_SITE_URL,
    bundle: {
      optimizeTranslationDirective: false,
    },
    defaultLocale: '',
    detectBrowserLanguage: false,
    experimental: {
      localeDetector: './locale-detector.ts',
    },
    locales: Object.entries(Languages).map(([key, value]) => ({
      code: key === 'English(all)' ? '' : `wordle-${name2slug(key)}`,
      iso: value.short,
      language: value.short,
      name: key,
    })),
    strategy: 'prefix_except_default',
    trailingSlash: true,
    vueI18n: './i18n.config.ts',
  },
  postcss: {
    plugins: {
      'postcss-import': {},
      'tailwindcss/nesting': {},
      tailwindcss: {},
      autoprefixer: {},
      ...(process.env.NODE_ENV === 'production' ? { cssnano: {} } : {}),
    },
  },
  vite: {
    define: {
      __IS_DEV__: process.env.NODE_ENV === 'development',
      __VERSION__: JSON.stringify(pkg.version),
    },
  },
})
