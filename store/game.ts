import type { <PERSON>Item, GamesRecord, GameState, HistoryPayload, IDictionary, LetterNumbers, RowItem } from '~/types';
import { SPECIFIED_WORD, LETTER_RANGE, MIN_LETTER_NUMBER } from '~/data';
import dict from '~/data/dict';
import useStore from '~/store';

function createGame(word:string, masked?:string, topicId?: string | number):GameState {
  return {
    topicId,
    word,
    masked,
    startTime: Date.now(),
    lastUpdatedTime: Date.now(),
    done: false,
    win: false,
    rows: [],
  };
}

const useGameStore = defineStore('game', () => {
  const games = ref<Record<string, GamesRecord>>({});
  const gameState = computed<GameState | undefined>(() => {
    const store = useStore();
    if (!(store.langName in games.value)) {
      games.value[ store.langName ] = {};
    }
    const idx = store.isSpecifiedWord ? SPECIFIED_WORD : store.letterNumber;
    return games.value[ store.langName ][ idx ];
  });
  const letterScore = computed<IDictionary<number>>(() => {
    if (!gameState.value) return {};
    return gameState.value.rows.flat().reduce((memo:IDictionary<number>, item:CellItem) => {
      const { letter, score } = item;
      memo[ letter ] = Math.max(memo[ letter ] || 0, score);
      return memo;
    }, {});
  });
  const level = computed<number>(() => {
    return gameState.value?.rows.length || 0;
  });

  function addHistory(history:HistoryPayload) {
    const store = useStore();
    store.addHistory(history.win, history.level);
    if (!gameState.value) return;
    gameState.value.done = true;
    gameState.value.win = history.win;
    store.saveLocal();
  }
  function addRowRecord(payload:RowItem) {
    if (!gameState.value) return;
    gameState.value.rows.push(payload);
    gameState.value.lastUpdatedTime = Date.now();
    const store = useStore();
    store.saveLocal();
  }
  function createGameByLetter(letterNumber:LetterNumbers) {
    const currentDict = dict[ letterNumber - MIN_LETTER_NUMBER + LETTER_RANGE ]; // select word from high-frequency words

    // 防御性检查：确保字典存在且不为空
    if (!currentDict || currentDict.length === 0) {
      console.warn(`Dictionary for letter ${letterNumber} is empty or not loaded`);
      // 使用备用词汇或等待字典加载
      const fallbackWord = 'a'.repeat(letterNumber); // 临时备用词
      const store = useStore();
      if (!games.value[ store.langName ]) {
        games.value[ store.langName ] = {};
      }
      games.value[ store.langName ][ letterNumber ] = createGame(fallbackWord);
      return;
    }

    const wordIndex = currentDict.length * Math.random() >> 0;
    const word = currentDict[ wordIndex ];

    // 再次检查选中的词是否有效
    if (!word || typeof word !== 'string') {
      console.warn(`Selected word is invalid: ${word}`);
      const fallbackWord = currentDict[ 0 ] || 'a'.repeat(letterNumber);
      const store = useStore();
      if (!games.value[ store.langName ]) {
        games.value[ store.langName ] = {};
      }
      games.value[ store.langName ][ letterNumber ] = createGame(fallbackWord);
      return;
    }

    const store = useStore();
    if (!games.value[ store.langName ]) {
      games.value[ store.langName ] = {};
    }
    games.value[ store.langName ][ letterNumber ] = createGame(word);
  }
  function reset() {
    const store = useStore();
    createGameByLetter(store.letterNumber);
  }
  function restart(force = true) {
    if (!force && gameState.value && !gameState.value.done) return;
    // if gameState.value not exist, it means it's the first time to play this letterNumber
    // so need to create a new game

    const store = useStore();
    createGameByLetter(store.letterNumber);
  }
  function retry() {
    if (!gameState.value) return;
    gameState.value.done = false;
    gameState.value.rows = [];
    gameState.value.lastUpdatedTime = Date.now();
  }
  function setGames(value: Record<string, GamesRecord>) {
    games.value = value;
  }
  function setWord(word: string, tid: string, masked: string) {
    const store = useStore();
    if (games.value[ store.langName ][ SPECIFIED_WORD ]?.word !== word
      || games.value[ store.langName ][ SPECIFIED_WORD ]?.topicId !== tid
    ) {
      games.value[ store.langName ][ SPECIFIED_WORD ] = createGame(word, masked, tid);
    }
  }

  return {
    games,
    gameState,
    letterScore,
    level,

    addHistory,
    addRowRecord,
    createGameByLetter,
    reset,
    restart,
    retry,
    setGames,
    setWord,
  };
});

export default useGameStore;
