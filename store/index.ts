import { MAX_STEP_BASE, Medals, MIN_LETTER_NUMBER } from '~/data';
import useGameStore from '~/store/game';
import type {
  ApiResponse,
  CustomGame,
  CustomProgressPayload,
  History,
  TopicCate,
  LetterNumbers,
  Topic,
  Config,
} from '~/types';
import mapValues from 'lodash-es/mapValues';
import { doMagic } from '~/utils/abracadabra';
import { slug2name } from '~/utils';
import dictionaries, { loadDict } from '~/data/dict';

const LOCAL_KEY = 'wordle';

function createDefaultConfig(): Config {
  return {
    autoDarkMode: true,
    currentCateName: '',
    fixedTopic: -1,
    highContrast: false,
    letterNumber: 5,
  };
}
function createDefaultCustom(): CustomGame {
  return {
    title: '',
    words: [],
  };
}
function createDefaultHistory(): History {
  return {
    played: 0,
    win: 0,
    streak: 0,
    maxStreak: 0,
    stats: [0, 0, 0, 0, 0, 0],
    lastWin: false,
  };
}

const useStore = defineStore('store', () => {
  const route = useRoute();
  const req = useRequestURL();
  const runtimeConfig = useRuntimeConfig();

  const config = useCookie<Config>('wordle-config', {
    default() {
      return createDefaultConfig();
    },
  });
  const customInfo = ref<CustomGame>(createDefaultCustom());
  const history = ref<History>(createDefaultHistory());
  const isHighContrast = ref<boolean>(false);
  const isLoading = ref<boolean>(false);
  const langSlug = ref<string>(route.params.slug as string || '');
  const keyboardDisabled = ref<boolean>(false);
  const topicProgress = ref<string[]>([]);
  const topicCates = ref<TopicCate[]>([]);
  const dict = computed<string[]>(() => {
    return dictionaries[ letterNumber.value - MIN_LETTER_NUMBER ];
  });
  const fixedTopic = computed<Topic | null>(() => {
    if (req.searchParams.get('c')) {
      const title = req.searchParams.get('topicName') || '';
      const words = (req.searchParams.get('words') || '').split(',');
      const pathname = req.pathname.replace(/^\//, '');
      return {
        title,
        topicId: -1,
        topicItems: words.map((hashed, i) => {
          hashed = unescape(hashed);
          const word = undoMagic(hashed);
          const medal = i < 3 ? Medals[ i ] : `${i + 1}`;
          const placeholder = new Array(word.length).fill('?').join('');
          return {
            medal,
            placeholder,
            url: `${pathname}?c=1&topicName=${title}&words=${words.join(',')}&w=${hashed}`,
            word: undoMagic(hashed),
            id: i,
            hashed,
          };
        })
      };
    }

    for (const category of theTopicCategories.value) {
      for (const title in category.topics) {
        if (category.topics[ title ].topicId === config.value.fixedTopic) {
          return { title, ...category.topics[ title ] };
        }
      }
    }
    return null;
  });
  const isCustom = computed<boolean>(() => {
    return !!route.query.c;
  });
  const isSpecifiedWord = computed<boolean>(() => {
    return !!route.query.w;
  });
  const isKeyboardDisabled = ref<boolean>(false);
  const langName = computed<string>(() => {
    return slug2name(langSlug.value);
  });
  const letterNumber = computed<LetterNumbers>(() => {
    return isCustom.value ? masked.value.length as LetterNumbers : config.value.letterNumber;
  });
  const masked = computed<string>(() => {
    const w = route.query.w as string || '';
    if (w) return w;
    if (!route.query.words) return '';

    const words = unescape(route.query.words as string).split(',');
    return words[ 0 ];
  });
  const maxStep = computed<number>(() => {
    return letterNumber.value - MIN_LETTER_NUMBER + MAX_STEP_BASE;
  });
  const theTopics = computed<Record<string, Topic>>(() => {
    const cate = theTopicCategories.value
      .find(item => item.category === config.value.currentCateName);
    return cate ? cate.topics : {};
  });
  const theTopicCategories = computed<{category: string, topics: Record<string, Topic>}[]>(() => {
    const path = route.path.replace(/^\//, '');
    const result = topicCates.value.map(cate => {
      return {
        category: cate.category,
        topics: mapValues(cate.topics, ({ id, words }) => {
          return {
            topicId: id,
            topicItems: words.map((word, i) => {
              word = word.toLowerCase();
              const medal = i < 3 ? Medals[ i ] : `${i + 1}`;
              const placeholder = new Array(word.length).fill('?').join('');
              const hashed = doMagic(word);
              const url = `${path}?w=${hashed}&tid=${id}`;
              return {
                medal,
                placeholder,
                url,
                word,
                id,
                hashed,
              };
            }),
          };
        }),
      };
    });
    const { isShared, topicName, words } = parseSearch();
    if (isShared) {
      result.unshift({
        category: topicName,
        topics: {
          [ topicName ]: {
            topicId: -1,
            topicItems: words.map((word, i) => {
              word = word.toLowerCase();
              const medal = i < 3 ? Medals[ i ] : `${i + 1}`;
              const placeholder = new Array(word.length).fill('?').join('');
              const hashed = doMagic(word);
              const url = `${path}?c=1&topicName=${topicName}&w=${hashed}&words=${words.join(',')}`;
              return {
                medal,
                placeholder,
                url,
                word,
                id: i,
                hashed,
              };
            }),
          },
        },
      });
    }
    return result;
  });

  function addHistory(isWin: boolean, level: number) {
    history.value.played += 1;
    history.value.win += isWin ? 1 : 0;

    if (!isWin) {
      history.value.streak = 0;
      history.value.lastWin = false;
      return;
    }

    if (history.value.stats.length < level) {
      for (let i = history.value.stats.length, len = level; i < len; i++) {
        history.value.stats[ i ] = history.value.stats[ i ] || 0;
      }
    }
    history.value.stats[ level - 1 ] += 1;
    if (history.value.lastWin) {
      history.value.streak += 1;
      if (history.value.streak > history.value.maxStreak) {
        history.value.maxStreak = history.value.streak;
      }
    } else {
      history.value.streak = 1;
      history.value.lastWin = true;
    }
    saveLocal();
  }
  function clearLocal() {
    localStorage.removeItem(LOCAL_KEY);
    const gameStore = useGameStore();
    gameStore.setGames({});
    config.value = createDefaultConfig();
    history.value = createDefaultHistory();
    topicProgress.value = [];
    customInfo.value = createDefaultCustom();
    gameStore.restart(); // reset all state, e.g. game-grid local data
  }
  function createInitialGame(): void {
    const gameStore = useGameStore();
    const tid = route.query.tid as string || '-1';
    if (masked.value) {
      const word = undoMagic(masked.value);
      setConfig({ letterNumber: word.length as LetterNumbers });
      gameStore.setWord(word, tid, masked.value);
      return;
    }

    // load next word from fixed topic
    if (fixedTopic.value) {
      isLoading.value = true;
      return;
    }

    if (!gameStore.gameState) {
      gameStore.createGameByLetter(config.value.letterNumber);
    }
  }
  function init() {
    const localData = localStorage.getItem(LOCAL_KEY);
    if (!localData) return;

    const parsed = JSON.parse(localData);
    history.value = parsed.history;
    topicProgress.value = parsed.progress;
    customInfo.value = parsed.customInfo;
    const gameStore = useGameStore();
    gameStore.setGames(parsed.games);
  }
  async function loadTopics() {
    const url = new URL(`${runtimeConfig.public.siteUrl}/api/top10`);
    if (route.params.slug) {
      url.searchParams.set('lang', route.params.slug as string);
    }
    const response = await $fetch<ApiResponse<TopicCate[]>>(url.toString());
    topicCates.value = response.data;
    if (!config.value.currentCateName) {
      // set the first category as current
      config.value.currentCateName = topicCates.value[ 0 ]?.category || '';
    }
    return response.data;
  }
  function setConfig(newConfig: Partial<Config>) {
    const oldValue = { ...config.value };
    if ('darkMode' in newConfig) {
      delete oldValue.autoDarkMode;
      document.documentElement.classList.toggle('dark', newConfig.darkMode);
    }
    config.value = {
      ...oldValue,
      ...newConfig,
    };
  }
  function setCustomGame(title: string, words: string[]) {
    customInfo.value.title = title;
    customInfo.value.words = words;
    saveLocal();
  }
  function setCustomProgress(payload:CustomProgressPayload) {
    const { word, topicId } = payload;
    const combination = `${topicId}-${word}`
    // limit the size of progress storage
    if (topicProgress.value.length > 1024) {
      topicProgress.value.shift();
    }
    topicProgress.value.push(combination)
    saveLocal();
  }
  function setIsLoading(value: boolean) {
    isLoading.value = value;
  }
  function setKeyboardDisabled(val: boolean) {
    isKeyboardDisabled.value = val;
  }
  async function setLetterNumber(payload:LetterNumbers) {
    isLoading.value = true;
    const gameStore = useGameStore();
    await loadDict(payload, langName.value);
    setConfig({ letterNumber: payload });
    gameStore.restart(false);
    isLoading.value = false;
  }
  function saveLocal() {
    const gameStore = useGameStore();
    localStorage.setItem(LOCAL_KEY, JSON.stringify({
      games: gameStore.games,
      history: history.value,
      progress: topicProgress.value,
      customInfo: customInfo.value,
    }));
  }

  return {
    config,
    customInfo,
    dict,
    fixedTopic,
    history,
    isCustom,
    isSpecifiedWord,
    isHighContrast,
    isKeyboardDisabled,
    isLoading,
    keyboardDisabled,
    langSlug,
    langName,
    letterNumber,
    masked,
    maxStep,
    theTopics,
    theTopicCategories,
    topicProgress,
    topicCates,

    addHistory,
    clearLocal,
    createInitialGame,
    init,
    loadTopics,
    saveLocal,
    setConfig,
    setCustomGame,
    setCustomProgress,
    setIsLoading,
    setKeyboardDisabled,
    setLetterNumber,
  };
});

export default useStore;
