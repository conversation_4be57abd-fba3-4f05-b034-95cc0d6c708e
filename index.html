<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{title}}</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="{{description}}">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">
    <link rel="canonical" href="{{canonical_url}}">
    <meta name="twitter:domain" content="https://mywordle.org">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{title}}">
    <meta name="twitter:description" content="{{description}}">
    <meta name="twitter:image:src" content="{{brand_img}}">
    <meta name="twitter:url" content="{{canonical_url}}">
    <meta property="og:title" content="{{title}}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{canonical_url}}">
    <meta property="og:image" content="{{brand_img}}">
    <meta property="og:image:alt" content="{{description}}">
    <meta property="og:description" content="{{description}}">
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@graph": [
          {
            "@type": "Organization",
            "@id": "{{canonical_url_org}}",
            "name": "{{title}}",
            "url": "{{canonical_url}}",
            "sameAs": []
          },
          {
            "@type": "WebSite",
            "@id": "{{canonical_url_website}}",
            "url": "{{canonical_url}}",
            "name": "{{title}}",
            "publisher": {
              "@id": "{{canonical_url_org}}"
            }
          },
          {
            "@type": "WebPage",
            "@id": "{{canonical_url_webpage}}",
            "url": "{{canonical_url}}",
            "inLanguage": "{{lang_code}}",
            "name": "{{h1}}",
            "isPartOf": {
              "@id": "{{canonical_url_website}}"
            },
            "image": {
              "@type": "ImageObject",
              "@id": "{{canonical_url_primary_image}}",
              "url": "{{brand_img}}",
              "width": 1024,
              "height": 1024
            },
            "primaryImageOfPage": {
              "@id": "{{canonical_url_primary_image}}"
            },
            "datePublished": "{{date_published}}",
            "description": "{{description}}"
          }
        ]
      }
    </script>
    <script>
      let isLocal = false;
      const local = localStorage.getItem('wordle');
      if (local) {
        const result = /"darkMode":(true|false)/.exec(local);
        if (result) {
          isLocal = true;
          if (result[1] === 'true') {
            document.documentElement.classList.add('dark');
          }
        }
      }
      if (!isLocal && matchMedia('(prefers-color-scheme: dark)').matches) {
        document.documentElement.classList.add('dark');
      }
    </script>
  </head>
  <body class="m-0">
    <header id="banner-ad" class="color-bg overflow-hidden">
      <div class="ezoic-ads" id="ezoic-pub-ad-placeholder-101"></div>
    </header>
    <div id="app" class="max-auto" data-h1="{{h1}}">
      <header id="header" class="text-2xl leading-12 text-center whitespace-nowrap">
        <h1 class="inner">{{h1}}</h1>
      </header>
      <div class="aw-container">
        <div class="grid grid-cols-5 w-9/12 gap-2.5 py-8 m-auto sketch"><!-- skeleton --></div>
      </div>
      <div class="keyboard-placeholder"></div>
      <!-- categories -->
      <div class="color-bg">
        <footer id="themes-list" class="pt-4 pb-2 container mx-auto lg:columns-2 xl:columns-3"><!-- themes --></footer>
      </div>
    </div>
    <!-- footer -->
    <footer class="color-bg">
      <div id="languages" class="pt-4 pb-10 mx-auto aw-container">
        <h2 class="mb-2 text-center text-lg">Play wordle unlimited in other languages:</h2>
        <div class="grid grid-cols-3 xs:grid-cols-2" id="languages-list"><!-- language-list --></div>
      </div>
    </footer>
    <script type="module" src="/src/main.ts"></script>
    <script async src="https://www.ezojs.com/ezoic/sa.min.js"></script>

    <!-- Adsense not approved yet <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4502790166243588"
     crossorigin="anonymous"></script> -->
  </body>
</html>
