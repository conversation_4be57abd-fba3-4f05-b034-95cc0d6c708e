<script lang="ts" setup>
import { sleep } from '~/utils';
import useStore from '~/store';

const store = useStore();
const config = useRuntimeConfig();

onMounted(async () => {
  store.init();
  // handle dark mode
  if (store.config.autoDarkMode) {
    if (window.matchMedia('(prefers-color-scheme: dark)')) {
      document.documentElement.classList.add('dark');
    }
    // listen for dark mode change
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (e.matches) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    });
  }

  if (__IS_DEV__) return;

  await sleep(2000);

  // setting for social share plugin, may remove or update later
  window._rsbtxt = [
    'Tweet',
    'Share on Facebook',
    'Copy Link',
    'Link Copied :)',
    'Wordlegame' // #hashtag
  ];
  const metaDesc = document.querySelector('[name="description"]');
  window.prestige = (metaDesc instanceof HTMLMetaElement) ? metaDesc.content : '';
  window.og_result_img = config.public.siteUrl + '/wordlebig.png';
  window.og_result_url = location.href;
  window.og_link = window.og_result_url;
  window.test = {
    'name': 'home',
    'lang': 'en',
    url: location.href,
  };
  // social share plugin need jquery, also may remove later
  const jqScript = document.createElement('script');
  jqScript.src = 'https://code.jquery.com/jquery-3.6.3.slim.min.js';
  jqScript.async = true;
  document.body.appendChild(jqScript);
  await sleep(100);
  const script = document.createElement('script');
  script.src = 'https://www.arealme.com/static/smm.js';
  script.async = true;
  document.body.appendChild(script);

  const dataLayer = (window.dataLayer = window.dataLayer || []) as IArguments[];
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
  function gtag(...args: any[]){ dataLayer.push(arguments); } // eslint-disable-line prefer-rest-params
  gtag('js', new Date());
  gtag('config', config.public.googleGaId);
});
</script>

<template>
  <NuxtRouteAnnouncer />
  <NuxtLoadingIndicator />
  <nuxt-layout>
    <nuxt-page />
  </nuxt-layout>
</template>
