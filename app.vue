<script lang="ts" setup>
import { sleep } from '~/utils';
import useStore from '~/store';

const store = useStore();
const config = useRuntimeConfig();

onMounted(async () => {
  store.init();
  // handle dark mode
  if (store.config.autoDarkMode) {
    if (window.matchMedia('(prefers-color-scheme: dark)')) {
      document.documentElement.classList.add('dark');
    }
    // listen for dark mode change
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (e.matches) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    });
  }

  if (__IS_DEV__) return;

  await sleep(2000);
  const dataLayer = (window.dataLayer = window.dataLayer || []) as IArguments[];
  function gtag(){ dataLayer.push(arguments); } // eslint-disable-line prefer-rest-params
  gtag('js', new Date());
  gtag('config', config.public.googleGaId);
});
</script>

<template>
  <NuxtRouteAnnouncer />
  <NuxtLoadingIndicator />
  <nuxt-layout>
    <nuxt-page />
  </nuxt-layout>
</template>
