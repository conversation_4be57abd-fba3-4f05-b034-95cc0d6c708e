<script setup lang="ts">
import { ChevronLeftIcon } from 'lucide-vue-next';
import useStore from '~/store';

const config = useRuntimeConfig();
const route = useRoute();
const store = useStore();
const { t } = useI18n();

const localeHead = useLocaleHead();
const title = computed(() => route.meta.title);
const globalLinks = ['/tos', '/privacy'];
const isGlobal = globalLinks.includes(route.path);

useHead({
  script: [
    {
      type: 'application',
      children: `{
  "@context": "https://schema.org/",
  "@type": "WebSite",
  "name": "${t('title')}",
  "image": "${config.public.siteUrl}/p.png",
  "description": "${t('description')}",
  "genre":"Utility",
  "copyrightYear": "2019",
  "url":"${config.public.siteUrl}",
  "author": {
    "@type":"WebSite",
    "name":"${config.public.siteUrl.replace(/https?:\/\//, '')}"
  },
  "publisher": {
    "@type":"Organization",
    "name":"${config.public.siteUrl.replace(/https?:\/\//, '')}",
    "logo":"${config.public.siteUrl}/p.png"
  }
}`,
    },
  ],
});
</script>

<template>
  <div class="min-h-full">
    <Html
      :lang="localeHead.htmlAttrs.lang"
      :class="{'dark': store.config.darkMode, 'high-contrast': store.config.highContrast }"
      :dir="localeHead.htmlAttrs.dir"
    >
    <Head>
      <Title>{{ title }}</Title>
      <template
        v-for="link in localeHead.link"
        :key="link.id"
      >
        <Link
          v-if="!isGlobal || globalLinks.includes(link.href)"
          :id="link.id"
          :rel="link.rel"
          :href="link.href"
          :hreflang="link.hreflang"
        />
      </template>
      <template
        v-for="meta in localeHead.meta"
        :key="meta.id"
      >
        <Meta
          :id="meta.id"
          :property="meta.property"
          :content="meta.content"
        />
      </template>
    </Head>
    <Body>
    <header class="navbar bg-base-200 mb-8">
      <nav class="w-full max-w-prose mx-auto text-xl">
        <nuxt-link
          class="btn btn-ghost btn-sm"
          to="/"
        >
          <chevron-left-icon class="size-4" />
          Back to Home
        </nuxt-link>
      </nav>
    </header>
    <slot />
    </Body>
    </Html>
  </div>
</template>
