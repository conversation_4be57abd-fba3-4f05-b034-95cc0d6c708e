<script lang="ts" setup>
import useStore from '~/store';

const config = useRuntimeConfig();
const route = useRoute();
const store = useStore();
const { t } = useI18n();

const localeHead = useLocaleHead();
const title = computed(() => route.meta.title);
const globalLinks = ['/terms-of-service', '/privacy'];
const isGlobal = globalLinks.includes(route.path);

useHead({
  script: [
    {
      type: 'application',
      innerHTML: `{
  "@context": "https://schema.org/",
  "@type": "WebSite",
  "name": "${t('title')}",
  "image": "${config.public.siteUrl}/p.png",
  "description": "${t('description')}",
  "genre":"Utility",
  "copyrightYear": "2019",
  "url":"${config.public.siteUrl}",
  "author": {
    "@type":"WebSite",
    "name":"${config.public.siteUrl.replace(/https?:\/\//, '')}"
  },
  "publisher": {
    "@type":"Organization",
    "name":"${config.public.siteUrl.replace(/https?:\/\//, '')}",
    "logo":"${config.public.siteUrl}/p.png"
  }
}`,
    },
  ],
});
</script>

<template>
  <div class="min-h-full">
    <Html
      :lang="localeHead.htmlAttrs.lang"
      :class="{'dark': store.config.darkMode, 'high-contrast': store.config.highContrast }"
      :dir="localeHead.htmlAttrs.dir"
    >
      <Head>
        <Title>{{ title }}</Title>
        <Meta name="google-adsense-account" content="ca-pub-****************" />
        <template
          v-for="link in localeHead.link"
          :key="link.id"
        >
          <Link
            v-if="!isGlobal || globalLinks.includes(link.href)"
            :id="link.id"
            :rel="link.rel"
            :href="link.href"
            :hreflang="link.hreflang"
          />
        </template>
        <template
          v-for="meta in localeHead.meta"
          :key="meta.id"
        >
          <Meta
            :id="meta.id"
            :property="meta.property"
            :content="meta.content"
          />
        </template>
      </Head>
      <Body>
        <slot />
        <app-footer />
        <other-languages />
      </Body>
    </Html>
  </div>
</template>

<script lang="ts">
export default {
  name: 'DefaultLayout',
}
</script>
