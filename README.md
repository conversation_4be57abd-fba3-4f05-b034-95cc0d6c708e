AW Wordle Game
==============

A wordle game built in Vue3 + Vite + TypeScript.


Setup
-----

1. Clone this repo to `/path/to/aw-wordle-game`
2. Install dependencies via `pnpm i`
3. Build project via `npm run build`
4. Go to dist folder via `cd dist`
5. Start a local static server, with auto redirecting, like `php -S localhost:9000`
6. Open `localhost:9000` in your browser


Development
-----------

1. Clone this repo to `/path/to/aw-wordle-game`
2. Install dependencies via `pnpm i`
3. Start dev server via `npm run dev`
4. Open `localhost:3000` in your browser


Deployment
----------

1. Redirect all missing visits to `/dist/index.html`
2. Add all locations in `/conf/nginx.conf` to your configuration
3. Build for mywordgame.com via `DOMAIN=https://mywordgame.com VIADS=https://amer.viavideo.digital/tag/load.js?sid=105383 npm run build`


Ads
---

1. Generate `ads.txt` via `npm run ad`
2. All `*.ads.txt` in `public/` will be merged and removed duplicate, into `ads.txt`

| Ads platform | URL | ads.txt | JS |
| ---- | ---- | ---- |----|
| Viads视频广告 | [后台入口](https://viads.com/publisher/statistics) / 用户名密码见email | 手动发送 | https://amer.viadata.store/tag/load.js?sid=105137 |
| Ezoic Ads | 生成中 | [从这里下载](https://srv.adstxtmanager.com/19390/mywordle.org) | https://www.ezojs.com/ezoic/sa.min.js            |


Operations
----------

### To update dictionary

1. Update `s*.txt` and `f*.txt` in `public/` folders
2. Change dictionary version `DICT_VERSION` in `src/data/dict.ts`
3. Run `npm run build`
